package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动点评Web接口
 */
@RestController
@RequestMapping("web/activity/review")
public class WebActivityReviewController extends AbstractController {

    @Autowired
    private ActivityTextService activityTextService;

    @Autowired
    private ActivityImageService activityImageService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    /**
     * 获取抖音点评内容
     */
    @GetMapping("/douyin")
    public R getDouyinReview(@RequestParam("activityId") Long activityId) {
        try {
            
        
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "抖音点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "douyin_review", "抖音点评");
        } catch (Exception e) {
            return R.error("获取抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取大众点评内容
     */
    @GetMapping("/dianping")
    public R getDianpingReview(@RequestParam("activityId") Long activityId) {
        try {
            
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "大众点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "dianping", "大众点评");
        } catch (Exception e) {
            return R.error("获取大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取美团点评内容
     */
    @GetMapping("/meituan")
    public R getMeituanReview(@RequestParam("activityId") Long activityId) {
        try {
            
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "美团点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "meituan", "美团点评");
        } catch (Exception e) {
            return R.error("获取美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取携程点评内容（兼容旧版）
     */
    @GetMapping("/ctrip")
    public R getCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type) throws Exception {


        // 根据type参数决定使用哪种广告类型
        String adType;
        String platformName;

        if ("notes".equals(type)) {
            adType = "ctrip_notes";
            platformName = "携程笔记";
        } else if ("review".equals(type)) {
            adType = "ctrip_review";
            platformName = "携程点评";
        } else {
            // 默认使用携程点评（兼容旧版）
            adType = "ctrip";
            platformName = "携程点评";
        }
        
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                 platformName + "转发"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }

        return getReviewContent(activityId, adType, platformName);
    }

    /**
     * 重新生成抖音点评内容
     */
    @RequestMapping("/regenerate/douyin")
    public R regenerateDouyinReview(@RequestParam("activityId") Long activityId) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "抖音点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, "douyin_review", "抖音点评");
        } catch (Exception e) {
            return R.error("重新生成抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成大众点评内容
     */
    @RequestMapping("/regenerate/dianping")
    public R regenerateDianpingReview(@RequestParam("activityId") Long activityId) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "大众点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, "dianping", "大众点评");
        } catch (Exception e) {
            return R.error("重新生成大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成美团点评内容
     */
    @RequestMapping("/regenerate/meituan")
    public R regenerateMeituanReview(@RequestParam("activityId") Long activityId) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "美团点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, "meituan", "美团点评");
        } catch (Exception e) {
            return R.error("重新生成美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成携程点评内容
     */
    @RequestMapping("/regenerate/ctrip")
    public R regenerateCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type) {
        try {
            // 根据type参数决定使用哪种广告类型
            String adType;
            String platformName;

            if ("notes".equals(type)) {
                adType = "ctrip_notes";
                platformName = "携程笔记";
            } else if ("review".equals(type)) {
                adType = "ctrip_review";
                platformName = "携程点评";
            } else {
                // 默认使用携程点评（兼容旧版）
                adType = "ctrip";
                platformName = "携程点评";
            }

            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    platformName + "重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, adType, platformName);
        } catch (Exception e) {
            return R.error("重新生成携程点评内容失败: " + e.getMessage());
        }
    }



    /**
     * 强制生成新的点评文案内容
     *
     * @throws Exception
     */
    private R forceGenerateNewReviewText(Long activityId, String adType, String platformName) throws Exception {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建新的文案实体
            ActivityTextEntity activityText = new ActivityTextEntity();
            activityText.setActivityId(activityId);
            activityText.setAdType(adType);
            activityText.setModel("deepseek-chat");

            // 强制调用AI生成新文案
            R generateResult = activityTextService.generateText(activityText, null);

            if (!generateResult.get("code").equals(200)) {
                return R.error("生成" + platformName + "文案失败: " + generateResult.get("msg"));
            }

            ActivityTextEntity newTextEntity = (ActivityTextEntity) generateResult.get("activityText");

            if (newTextEntity != null) {
                result.put("text", newTextEntity.getResult()); // 文案内容
                result.put("title", newTextEntity.getTitle()); // 提示词
                result.put("name", newTextEntity.getName()); // 标题
            } else {
                return R.error("生成" + platformName + "文案失败");
            }

            return R.ok().put("result", result);
        } catch (Exception e) {
            return R.error("生成" + platformName + "文案异常: " + e.getMessage());
        }
    }

    /**
     * 通用获取点评内容方法
     *
     * @throws Exception
     */
    private R getReviewContent(Long activityId, String adType, String platformName) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 查询对应平台的文案 - 使用现有的方法
        ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeNotUse(activityId, adType);

        // 查询对应平台的图片 - 使用按平台的方法
        List<ActivityImageEntity> imageList = activityImageService.findByActivityIdNoUseLimitByPlatform(activityId, adType, 3);

        // 构建返回数据
        result.put("platform", platformName);
        result.put("platformCode", adType);

        if (textEntity != null) {
            result.put("title", textEntity.getName() != null ? textEntity.getName() : textEntity.getTitle());
            result.put("content", textEntity.getResult());
            result.put("promptKeyword", textEntity.getQuery());
            result.put("textId", textEntity.getId());
            activityTextService.incrementUseCount(textEntity.getId());
        } else {
            result.put("title", "");
            result.put("content", "");
            result.put("promptKeyword", "");
            result.put("textId", null);
        }

        if (!imageList.isEmpty()) {
            result.put("images", imageList);
            result.put("hasImages", true);
            imageList.forEach(e -> {
                activityImageService.incrementUseCountByPlatform(e.getId(), adType, activityId);
            });
        } else {
            result.put("images", null);
            result.put("hasImages", false);
        }

        return R.ok().put("result", result);
    }
}
