package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 充值记录实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@Data
@TableName("activity_recharge_record")
@Accessors(chain = true)
public class ActivityRechargeRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动ID（创建活动套餐时为空，支付成功后填入）
     */
    private Long activityId;

    /**
     * 套餐ID
     */
    private Long packageId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 充值类型：1-套餐充值，2-自定义充值，3-赠送，4-创建活动套餐
     */
    private Integer rechargeType;

    /**
     * 充值次数
     */
    private Integer countValue;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 实际支付金额
     */
    private BigDecimal payAmount;

    /**
     * 状态：0-待支付，1-已支付，2-已取消，3-已退款，4-退款中
     */
    private Integer status;

    /**
     * 是否具有退款权限：0-无权限，1-有权限
     */
    private Integer refundEligible;

    /**
     * 退款权限分配时间
     */
    private Date refundQuotaAssignedTime;

    /**
     * 支付方式：1-微信支付，2-支付宝，3-余额支付
     */
    private Integer payType;

    /**
     * 支付流水号
     */
    private String payTransaction;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 来源：1-用户充值，2-系统赠送，3-活动奖励
     */
    private Integer source;

    /**
     * 创建活动套餐生成的活动ID
     */
    private Long createdActivityId;

    /**
     * 活动模板ID（创建活动套餐使用）
     */
    private Long activityTemplateId;

    /**
     * 活动名称（创建活动套餐使用）
     */
    private String activityNameForCreation;

    /**
     * 续费天数
     */
    private Integer renewalDays;

    /**
     * 续费前的原过期时间
     */
    private Date originalExpirationTime;

    /**
     * 续费后的新过期时间
     */
    private Date newExpirationTime;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 关联业务员ID
     */
    private Long salesmanId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    /**
     * 防重令牌
     */
    @TableField(exist = false)
    private String repeatToken;

    /**
     * 套餐名称（关联查询字段）
     */
    @TableField(exist = false)
    private String packageName;

    /**
     * 用户名称（关联查询字段）
     */
    @TableField(exist = false)
    private String userName;
    @TableField(exist = false)
    private String mobile;

    /**
     * 活动名称（关联查询字段）
     */
    @TableField(exist = false)
    private String activityName;

    /**
     * 业务员姓名（关联查询字段）
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 已使用次数（关联查询字段）
     */
    @TableField(exist = false)
    private Integer usedCount;

    /**
     * 剩余可用次数（关联查询字段）
     */
    @TableField(exist = false)
    private Integer remainingCount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 业务员编号（关联查询字段）
     */
    @TableField(exist = false)
    private String salesmanCode;

    /**
     * 佣金比例（关联查询字段）
     */
    @TableField(exist = false)
    private BigDecimal commissionRate;

    /**
     * 佣金金额（关联查询字段）
     */
    @TableField(exist = false)
    private BigDecimal commissionAmount;
}
