package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.dao.AdTypeConfigDao;
import com.cjy.pyp.modules.activity.entity.AdTypeConfigEntity;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Service("adTypeConfigService")
public class AdTypeConfigServiceImpl extends ServiceImpl<AdTypeConfigDao, AdTypeConfigEntity> implements AdTypeConfigService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        
        // 按类型名称搜索
        String typeName = (String) params.get("typeName");
        if (StringUtils.hasText(typeName)) {
            wrapper.like("type_name", typeName);
        }
        
        // 按状态过滤
        String status = (String) params.get("status");
        if (StringUtils.hasText(status)) {
            wrapper.eq("status", status);
        }
        
        // 按排序和创建时间排序
        wrapper.orderByAsc("sort_order").orderByDesc("create_on");

        IPage<AdTypeConfigEntity> page = this.page(
                new Query<AdTypeConfigEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }
    
    @Override
    public List<AdTypeConfigEntity> getEnabledConfigs() {
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        wrapper.orderByAsc("sort_order");
        return this.list(wrapper);
    }
    
    @Override
    public AdTypeConfigEntity getByTypeCode(String typeCode) {
        if (!StringUtils.hasText(typeCode)) {
            return null;
        }
        
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("type_code", typeCode);
        wrapper.eq("status", 1);
        return this.getOne(wrapper);
    }
    
    @Override
    public String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle) {
        return buildPrompt(typeCode, keyword, nameMode, manualTitle, null);
    }

    @Override
    public String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle, String userCustomInput) {
        AdTypeConfigEntity config = getByTypeCode(typeCode);
        if (config == null) {
            // 如果找不到配置，使用通用配置
            config = getByTypeCode("general");
        }

        if (config == null) {
            throw new RuntimeException("未找到广告类型配置: " + typeCode);
        }

        String promptTemplate = config.getPromptTemplate();

        // 替换模板中的占位符
        String prompt = promptTemplate
                .replace("{platform}", config.getPlatform())
                .replace("{content_type}", config.getContentType())
                .replace("{keyword}", keyword != null ? keyword : "")
                .replace("{title_length}", config.getTitleLength())
                .replace("{content_length}", config.getContentLength())
                .replace("{topics_count}", String.valueOf(config.getTopicsCount()))
                .replace("{topics_format}", config.getTopicsFormat())
                .replace("{requirements}", config.getRequirements())
                .replace("{style}", config.getStyle());

        // 处理标题部分
        String titleSection = "";
        if ("manual".equals(nameMode) && StringUtils.hasText(manualTitle)) {
            titleSection = "标题：" + manualTitle + "\n";
        }
        prompt = prompt.replace("{title_section}", titleSection);

        // 处理用户自定义输入部分
        String userInputSection = "";
        if (StringUtils.hasText(userCustomInput)) {
            userInputSection = "\n\n用户补充要求：" + userCustomInput;
        }
        prompt = prompt + userInputSection;

        return prompt;
    }

    @Override
    public void optimizeNotesPrompts() {
        // 优化小红书配置
        optimizeXiaohongshuPrompt();

        // 优化携程笔记配置
        optimizeCtripNotesPrompt();

        // 添加新的笔记类型配置
        addNewNotesConfigs();

        // 优化通用文案配置
        optimizeGeneralPrompt();
    }

    private void optimizeXiaohongshuPrompt() {
        AdTypeConfigEntity config = getByTypeCode("xiaohongshu");
        if (config != null) {
            config.setRequirements("- 必须有真实的个人体验和情感表达，避免空洞的描述\n" +
                    "- 要有具体的场景细节，让读者有画面感和代入感\n" +
                    "- 内容要有实用价值，提供可操作的建议或攻略\n" +
                    "- 语言要自然流畅，像朋友间的真诚分享\n" +
                    "- 要有适度的情感起伏，表达真实的感受和惊喜\n" +
                    "- 避免过度营销化的语言，保持种草的自然性\n" +
                    "- 要有独特的个人视角和见解，不要千篇一律");

            config.setStyle("真实自然、有温度、个人化、实用性强、情感丰富");

            config.setPromptTemplate("你是一位热爱生活、善于分享的小红书博主，请为{platform}平台创作一篇关于{keyword}的{content_type}内容。\n\n" +
                    "【创作要求】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【创作指导】\n1. 开头要有吸引力：用一个有趣的场景、意外的发现或强烈的感受开始\n" +
                    "2. 中间要有干货：提供具体的信息、技巧或攻略，让读者有收获\n" +
                    "3. 结尾要有共鸣：用疑问句、感叹句或互动语言引发读者参与\n\n" +
                    "【语言风格】\n- 用第一人称，分享真实体验\n- 适当使用感叹词和语气词（哇、真的、超级、绝了等）\n" +
                    "- 多用短句，节奏感强\n- 适当加入emoji表情（但不要过度）\n- 避免官方化、广告化的表达\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和点击欲望）\n" +
                    "- 内容（content，{content_length}，注意：内容中不要包含话题标签，要有真实的体验感和实用价值）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要精准匹配内容主题）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}（每个话题前加#号，用空格分隔）\n" +
                    "- 内容要像真人写的，有个人特色和情感温度\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeCtripNotesPrompt() {
        AdTypeConfigEntity config = getByTypeCode("ctrip_notes");
        if (config != null) {
            config.setRequirements("- 要有真实的旅行体验和感受，避免空泛的描述\n" +
                    "- 提供实用的旅行信息：交通、住宿、美食、景点等具体攻略\n" +
                    "- 要有个人的旅行故事和独特发现，让内容有温度\n" +
                    "- 包含具体的时间、地点、价格等实用信息\n" +
                    "- 要有美好的画面描述，让读者产生向往\n" +
                    "- 提供贴心的旅行建议和注意事项\n" +
                    "- 语言要轻松愉快，传递旅行的美好");

            config.setStyle("轻松愉快、实用详细、有画面感、温暖治愈、充满向往");

            config.setPromptTemplate("你是一位经验丰富、热爱分享的旅行达人，请为{platform}平台创作一篇关于{keyword}的旅行{content_type}。\n\n" +
                    "【创作背景】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容要求】\n{requirements}\n\n" +
                    "【创作框架】\n1. 开场吸引：用一个美好的旅行瞬间或意外发现开始\n" +
                    "2. 体验分享：详细描述真实的旅行体验和感受\n" +
                    "3. 实用攻略：提供具体的旅行信息和建议\n" +
                    "4. 情感升华：表达旅行带来的美好感受和收获\n\n" +
                    "【写作技巧】\n- 用生动的描述营造画面感，让读者仿佛身临其境\n" +
                    "- 提供具体的数字信息（价格、时间、距离等）增加可信度\n" +
                    "- 分享个人的小贴士和独家发现\n" +
                    "- 用温暖的语言传递旅行的美好和治愈力\n" +
                    "- 适当加入感官描述（视觉、听觉、味觉等）\n\n" +
                    "【语言特色】\n- 轻松自然，像朋友间的分享\n- 充满画面感和情感色彩\n" +
                    "- 实用性强，信息丰富\n- 积极正面，传递美好\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和旅行感）\n" +
                    "- 内容（content，{content_length}，要有真实体验感和实用价值，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与旅行主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让读者感受到旅行的美好，产生出行的冲动\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void addNewNotesConfigs() {
        // 为知乎添加笔记类配置（如果不存在）
        if (getByTypeCode("zhihu_notes") == null) {
            AdTypeConfigEntity zhihuConfig = new AdTypeConfigEntity();
            zhihuConfig.setTypeCode("zhihu_notes");
            zhihuConfig.setTypeName("知乎笔记");
            zhihuConfig.setPlatform("知乎");
            zhihuConfig.setContentType("知识分享");
            zhihuConfig.setTitleLength("50字以内，有思考深度");
            zhihuConfig.setContentLength("300-800字，深度分析");
            zhihuConfig.setTopicsCount(5);
            zhihuConfig.setTopicsFormat("不带#号，用逗号分隔");
            zhihuConfig.setRequirements("- 要有深度的思考和独特的见解，避免浅层的描述\n" +
                    "- 提供有价值的知识和经验分享\n" +
                    "- 逻辑清晰，论证有力，有理有据\n" +
                    "- 要有个人的实践经验和案例\n" +
                    "- 语言专业但不失亲和力\n" +
                    "- 要能解决读者的实际问题\n" +
                    "- 避免空洞的理论，要有实操性");
            zhihuConfig.setStyle("专业深度、逻辑清晰、实用性强、有见解");
            zhihuConfig.setPromptTemplate("你是一位在{keyword}领域有丰富经验的专业人士，请为{platform}平台创作一篇有深度的{content_type}内容。\n\n" +
                    "【创作主题】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【创作结构】\n1. 问题引入：提出一个有价值的问题或现象\n" +
                    "2. 深度分析：从多个角度分析问题的本质和原因\n" +
                    "3. 实践经验：分享个人的实际经验和案例\n" +
                    "4. 解决方案：提供具体可行的建议和方法\n" +
                    "5. 总结升华：给出有价值的思考和启发\n\n" +
                    "【写作要求】\n- 逻辑严密，层次分明\n- 有理有据，避免空洞说教\n" +
                    "- 结合实际案例和数据\n- 语言专业但易懂\n" +
                    "- 要有独特的个人见解\n- 能够解决读者的实际问题\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有思考深度和吸引力）\n" +
                    "- 内容（content，{content_length}，要有深度分析和实用价值，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与专业领域相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要体现专业性和深度，让读者有收获\n\n风格特点：{style}");
            zhihuConfig.setSortOrder(50);
            zhihuConfig.setStatus(1);
            this.save(zhihuConfig);
        }
    }

    private void optimizeGeneralPrompt() {
        AdTypeConfigEntity config = getByTypeCode("general");
        if (config != null) {
            config.setRequirements("- 内容要有实用价值，能解决实际问题\n" +
                    "- 语言要通俗易懂，适合大众阅读\n" +
                    "- 要有清晰的逻辑结构和条理\n" +
                    "- 要有个人的经验和见解分享\n" +
                    "- 避免空洞的理论，要有具体的例子\n" +
                    "- 要有一定的深度，不能过于浅显\n" +
                    "- 适合多平台传播和分享");

            config.setStyle("实用性强、通俗易懂、有深度、适应性强");

            config.setPromptTemplate("你是一位经验丰富的内容创作者，请为{platform}平台创作一篇关于{keyword}的{content_type}内容。\n\n" +
                    "【创作主题】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【创作原则】\n1. 实用为王：内容要能解决读者的实际问题\n" +
                    "2. 通俗易懂：用简单的语言表达复杂的概念\n" +
                    "3. 有理有据：提供具体的例子和数据支撑\n" +
                    "4. 个人化：加入个人的经验和独特见解\n" +
                    "5. 结构清晰：逻辑分明，便于理解\n\n" +
                    "【语言要求】\n- 简洁明了，不拖泥带水\n- 贴近生活，有亲和力\n" +
                    "- 专业但不失通俗\n- 有说服力和可信度\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和实用价值）\n" +
                    "- 内容（content，{content_length}，要有实用性和深度，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让读者有收获，有实际帮助\n\n风格特点：{style}");

            this.updateById(config);
        }
    }
}
