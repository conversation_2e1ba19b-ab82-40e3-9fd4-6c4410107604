package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.dao.AdTypeConfigDao;
import com.cjy.pyp.modules.activity.entity.AdTypeConfigEntity;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Service("adTypeConfigService")
public class AdTypeConfigServiceImpl extends ServiceImpl<AdTypeConfigDao, AdTypeConfigEntity> implements AdTypeConfigService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        
        // 按类型名称搜索
        String typeName = (String) params.get("typeName");
        if (StringUtils.hasText(typeName)) {
            wrapper.like("type_name", typeName);
        }
        
        // 按状态过滤
        String status = (String) params.get("status");
        if (StringUtils.hasText(status)) {
            wrapper.eq("status", status);
        }
        
        // 按排序和创建时间排序
        wrapper.orderByAsc("sort_order").orderByDesc("create_on");

        IPage<AdTypeConfigEntity> page = this.page(
                new Query<AdTypeConfigEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }
    
    @Override
    public List<AdTypeConfigEntity> getEnabledConfigs() {
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        wrapper.orderByAsc("sort_order");
        return this.list(wrapper);
    }
    
    @Override
    public AdTypeConfigEntity getByTypeCode(String typeCode) {
        if (!StringUtils.hasText(typeCode)) {
            return null;
        }
        
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("type_code", typeCode);
        wrapper.eq("status", 1);
        return this.getOne(wrapper);
    }
    
    @Override
    public String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle) {
        return buildPrompt(typeCode, keyword, nameMode, manualTitle, null);
    }

    @Override
    public String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle, String userCustomInput) {
        AdTypeConfigEntity config = getByTypeCode(typeCode);
        if (config == null) {
            // 如果找不到配置，使用通用配置
            config = getByTypeCode("general");
        }

        if (config == null) {
            throw new RuntimeException("未找到广告类型配置: " + typeCode);
        }

        String promptTemplate = config.getPromptTemplate();

        // 替换模板中的占位符
        String prompt = promptTemplate
                .replace("{platform}", config.getPlatform())
                .replace("{content_type}", config.getContentType())
                .replace("{keyword}", keyword != null ? keyword : "")
                .replace("{title_length}", config.getTitleLength())
                .replace("{content_length}", config.getContentLength())
                .replace("{topics_count}", String.valueOf(config.getTopicsCount()))
                .replace("{topics_format}", config.getTopicsFormat())
                .replace("{requirements}", config.getRequirements())
                .replace("{style}", config.getStyle());

        // 处理标题部分
        String titleSection = "";
        if ("manual".equals(nameMode) && StringUtils.hasText(manualTitle)) {
            titleSection = "标题：" + manualTitle + "\n";
        }
        prompt = prompt.replace("{title_section}", titleSection);

        // 处理用户自定义输入部分
        String userInputSection = "";
        if (StringUtils.hasText(userCustomInput)) {
            userInputSection = "\n\n用户补充要求：" + userCustomInput;
        }
        prompt = prompt + userInputSection;

        return prompt;
    }

    @Override
    public void optimizeNotesPrompts() {
        // 优化小红书配置
        optimizeXiaohongshuPrompt();

        // 优化携程笔记配置
        optimizeCtripNotesPrompt();

        // 优化大众点评配置
        optimizeDianpingPrompt();

        // 优化美团点评配置
        optimizeMeituanPrompt();

        // 优化微信朋友圈配置
        optimizeWeixinPrompt();

        // 优化携程点评配置
        optimizeCtripReviewPrompt();

        // 优化抖音点评配置
        optimizeDouyinReviewPrompt();

        // 添加新的笔记类型配置
        addNewNotesConfigs();

        // 优化通用文案配置
        optimizeGeneralPrompt();
    }

    private void optimizeXiaohongshuPrompt() {
        AdTypeConfigEntity config = getByTypeCode("xiaohongshu");
        if (config != null) {
            config.setRequirements("- 必须有真实的个人体验和情感表达，避免空洞的描述\n" +
                    "- 要有具体的场景细节，让读者有画面感和代入感\n" +
                    "- 内容要有实用价值，提供可操作的建议或攻略\n" +
                    "- 语言要自然流畅，像朋友间的真诚分享\n" +
                    "- 要有适度的情感起伏，表达真实的感受和惊喜\n" +
                    "- 避免过度营销化的语言，保持种草的自然性\n" +
                    "- 要有独特的个人视角和见解，不要千篇一律");

            config.setStyle("真实自然、有温度、个人化、实用性强、情感丰富");

            config.setPromptTemplate("你是一位热爱生活、善于分享的小红书博主，请为{platform}平台创作一篇关于{keyword}的{content_type}内容。\n\n" +
                    "【创作要求】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【创作指导】\n1. 开头要有吸引力：用一个有趣的场景、意外的发现或强烈的感受开始\n" +
                    "2. 中间要有干货：提供具体的信息、技巧或攻略，让读者有收获\n" +
                    "3. 结尾要有共鸣：用疑问句、感叹句或互动语言引发读者参与\n\n" +
                    "【语言风格】\n- 用第一人称，分享真实体验\n- 适当使用感叹词和语气词（哇、真的、超级、绝了等）\n" +
                    "- 多用短句，节奏感强\n- 适当加入emoji表情（但不要过度）\n- 避免官方化、广告化的表达\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和点击欲望）\n" +
                    "- 内容（content，{content_length}，注意：内容中不要包含话题标签，要有真实的体验感和实用价值）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要精准匹配内容主题）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}（每个话题前加#号，用空格分隔）\n" +
                    "- 内容要像真人写的，有个人特色和情感温度\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeCtripNotesPrompt() {
        AdTypeConfigEntity config = getByTypeCode("ctrip_notes");
        if (config != null) {
            config.setRequirements("- 要有真实的旅行体验和感受，避免空泛的描述\n" +
                    "- 提供实用的旅行信息：交通、住宿、美食、景点等具体攻略\n" +
                    "- 要有个人的旅行故事和独特发现，让内容有温度\n" +
                    "- 包含具体的时间、地点、价格等实用信息\n" +
                    "- 要有美好的画面描述，让读者产生向往\n" +
                    "- 提供贴心的旅行建议和注意事项\n" +
                    "- 语言要轻松愉快，传递旅行的美好");

            config.setStyle("轻松愉快、实用详细、有画面感、温暖治愈、充满向往");

            config.setPromptTemplate("你是一位经验丰富、热爱分享的旅行达人，请为{platform}平台创作一篇关于{keyword}的旅行{content_type}。\n\n" +
                    "【创作背景】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容要求】\n{requirements}\n\n" +
                    "【创作框架】\n1. 开场吸引：用一个美好的旅行瞬间或意外发现开始\n" +
                    "2. 体验分享：详细描述真实的旅行体验和感受\n" +
                    "3. 实用攻略：提供具体的旅行信息和建议\n" +
                    "4. 情感升华：表达旅行带来的美好感受和收获\n\n" +
                    "【写作技巧】\n- 用生动的描述营造画面感，让读者仿佛身临其境\n" +
                    "- 提供具体的数字信息（价格、时间、距离等）增加可信度\n" +
                    "- 分享个人的小贴士和独家发现\n" +
                    "- 用温暖的语言传递旅行的美好和治愈力\n" +
                    "- 适当加入感官描述（视觉、听觉、味觉等）\n\n" +
                    "【语言特色】\n- 轻松自然，像朋友间的分享\n- 充满画面感和情感色彩\n" +
                    "- 实用性强，信息丰富\n- 积极正面，传递美好\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和旅行感）\n" +
                    "- 内容（content，{content_length}，要有真实体验感和实用价值，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与旅行主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让读者感受到旅行的美好，产生出行的冲动\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void addNewNotesConfigs() {
        // 为知乎添加笔记类配置（如果不存在）
        if (getByTypeCode("zhihu_notes") == null) {
            AdTypeConfigEntity zhihuConfig = new AdTypeConfigEntity();
            zhihuConfig.setTypeCode("zhihu_notes");
            zhihuConfig.setTypeName("知乎笔记");
            zhihuConfig.setPlatform("知乎");
            zhihuConfig.setContentType("知识分享");
            zhihuConfig.setTitleLength("50字以内，有思考深度");
            zhihuConfig.setContentLength("300-800字，深度分析");
            zhihuConfig.setTopicsCount(5);
            zhihuConfig.setTopicsFormat("不带#号，用逗号分隔");
            zhihuConfig.setRequirements("- 要有深度的思考和独特的见解，避免浅层的描述\n" +
                    "- 提供有价值的知识和经验分享\n" +
                    "- 逻辑清晰，论证有力，有理有据\n" +
                    "- 要有个人的实践经验和案例\n" +
                    "- 语言专业但不失亲和力\n" +
                    "- 要能解决读者的实际问题\n" +
                    "- 避免空洞的理论，要有实操性");
            zhihuConfig.setStyle("专业深度、逻辑清晰、实用性强、有见解");
            zhihuConfig.setPromptTemplate("你是一位在{keyword}领域有丰富经验的专业人士，请为{platform}平台创作一篇有深度的{content_type}内容。\n\n" +
                    "【创作主题】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【创作结构】\n1. 问题引入：提出一个有价值的问题或现象\n" +
                    "2. 深度分析：从多个角度分析问题的本质和原因\n" +
                    "3. 实践经验：分享个人的实际经验和案例\n" +
                    "4. 解决方案：提供具体可行的建议和方法\n" +
                    "5. 总结升华：给出有价值的思考和启发\n\n" +
                    "【写作要求】\n- 逻辑严密，层次分明\n- 有理有据，避免空洞说教\n" +
                    "- 结合实际案例和数据\n- 语言专业但易懂\n" +
                    "- 要有独特的个人见解\n- 能够解决读者的实际问题\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有思考深度和吸引力）\n" +
                    "- 内容（content，{content_length}，要有深度分析和实用价值，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与专业领域相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要体现专业性和深度，让读者有收获\n\n风格特点：{style}");
            zhihuConfig.setSortOrder(50);
            zhihuConfig.setStatus(1);
            this.save(zhihuConfig);
        }
    }

    private void optimizeGeneralPrompt() {
        AdTypeConfigEntity config = getByTypeCode("general");
        if (config != null) {
            config.setRequirements("- 内容要有实用价值，能解决实际问题\n" +
                    "- 语言要通俗易懂，适合大众阅读\n" +
                    "- 要有清晰的逻辑结构和条理\n" +
                    "- 要有个人的经验和见解分享\n" +
                    "- 避免空洞的理论，要有具体的例子\n" +
                    "- 要有一定的深度，不能过于浅显\n" +
                    "- 适合多平台传播和分享");

            config.setStyle("实用性强、通俗易懂、有深度、适应性强");

            config.setPromptTemplate("你是一位经验丰富的内容创作者，请为{platform}平台创作一篇关于{keyword}的{content_type}内容。\n\n" +
                    "【创作主题】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【创作原则】\n1. 实用为王：内容要能解决读者的实际问题\n" +
                    "2. 通俗易懂：用简单的语言表达复杂的概念\n" +
                    "3. 有理有据：提供具体的例子和数据支撑\n" +
                    "4. 个人化：加入个人的经验和独特见解\n" +
                    "5. 结构清晰：逻辑分明，便于理解\n\n" +
                    "【语言要求】\n- 简洁明了，不拖泥带水\n- 贴近生活，有亲和力\n" +
                    "- 专业但不失通俗\n- 有说服力和可信度\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和实用价值）\n" +
                    "- 内容（content，{content_length}，要有实用性和深度，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让读者有收获，有实际帮助\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeDianpingPrompt() {
        AdTypeConfigEntity config = getByTypeCode("dianping");
        if (config != null) {
            config.setRequirements("- 必须有真实的消费体验和具体感受，避免空洞描述\n" +
                    "- 要有详细的服务过程和产品体验描述\n" +
                    "- 提供具体的价格、环境、服务等实用信息\n" +
                    "- 要有个人的真实评价和建议\n" +
                    "- 语言要客观但有温度，像朋友推荐\n" +
                    "- 要对其他消费者有实际参考价值\n" +
                    "- 避免过度夸赞或贬低，保持真实性");

            config.setStyle("真实客观、详细实用、有温度、参考价值高");

            config.setPromptTemplate("你是一位有丰富消费经验的真实用户，请为{platform}平台写一篇关于{keyword}的{content_type}。\n\n" +
                    "【评价背景】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容要求】\n{requirements}\n\n" +
                    "【评价框架】\n1. 初印象：第一次到店或接触的感受\n" +
                    "2. 详细体验：服务过程、产品质量、环境氛围等具体描述\n" +
                    "3. 性价比分析：价格与价值的对比评价\n" +
                    "4. 推荐建议：给其他消费者的实用建议\n\n" +
                    "【写作要点】\n- 用具体的细节描述增加可信度\n" +
                    "- 提供实用的消费信息（价格、时间、注意事项等）\n" +
                    "- 分享个人的真实感受和评价\n" +
                    "- 给出中肯的建议和推荐理由\n" +
                    "- 语言自然真实，像朋友间的分享\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要突出体验亮点）\n" +
                    "- 内容（content，{content_length}，要有真实体验感和实用价值，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与商户特色相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n" +
                    "- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让其他用户感受到真实性和参考价值\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeMeituanPrompt() {
        AdTypeConfigEntity config = getByTypeCode("meituan");
        if (config != null) {
            config.setRequirements("- 要有真实的消费体验和性价比分析\n" +
                    "- 突出优惠信息和实际花费情况\n" +
                    "- 提供具体的消费建议和省钱技巧\n" +
                    "- 要有详细的产品和服务描述\n" +
                    "- 语言要实用导向，帮助其他消费者决策\n" +
                    "- 要有明确的推荐理由和注意事项\n" +
                    "- 避免夸大宣传，注重实际体验");

            config.setStyle("实用导向、性价比突出、消费建议、决策帮助");

            config.setPromptTemplate("你是一位精明的消费者，请为{platform}平台写一篇关于{keyword}的{content_type}。\n\n" +
                    "【消费背景】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【评价重点】\n1. 性价比分析：价格是否合理，是否物有所值\n" +
                    "2. 优惠体验：实际享受的优惠和省钱情况\n" +
                    "3. 产品服务：具体的产品质量和服务体验\n" +
                    "4. 消费建议：给其他消费者的实用建议和提醒\n\n" +
                    "【写作技巧】\n- 重点突出性价比和优惠信息\n" +
                    "- 提供具体的价格和消费数据\n" +
                    "- 分享实用的消费技巧和建议\n" +
                    "- 用真实的体验增加说服力\n" +
                    "- 帮助其他消费者做出明智选择\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要突出性价比优势）\n" +
                    "- 内容（content，{content_length}，要有实用建议和真实体验，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与优惠和特色相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n" +
                    "- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让其他消费者获得实用的消费指导\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeWeixinPrompt() {
        AdTypeConfigEntity config = getByTypeCode("weixin");
        if (config != null) {
            config.setRequirements("- 内容要有分享价值，能引起朋友关注和互动\n" +
                    "- 语言要自然亲切，像朋友间的真诚分享\n" +
                    "- 要有个人的真实体验和感受\n" +
                    "- 信息要准确可信，避免夸大宣传\n" +
                    "- 适合社交场景，不过于商业化\n" +
                    "- 要有一定的话题性和讨论价值\n" +
                    "- 内容要简洁明了，易于阅读和理解");

            config.setStyle("自然亲切、有分享价值、真实可信、适合社交");

            config.setPromptTemplate("你是一位热爱分享生活的朋友，请为{platform}创作一条关于{keyword}的{content_type}内容。\n\n" +
                    "【分享主题】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容要求】\n{requirements}\n\n" +
                    "【分享原则】\n1. 真实体验：分享真实的个人体验和感受\n" +
                    "2. 有用信息：提供对朋友有价值的信息和建议\n" +
                    "3. 自然表达：用朋友间聊天的自然语言\n" +
                    "4. 适度推荐：避免过度营销，保持真诚\n" +
                    "5. 引发互动：内容要有一定的话题性\n\n" +
                    "【语言特色】\n- 轻松自然，不刻意\n" +
                    "- 真诚分享，有温度\n" +
                    "- 简洁明了，易理解\n" +
                    "- 有个人特色和见解\n" +
                    "- 适合朋友圈的社交氛围\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和分享价值）\n" +
                    "- 内容（content，{content_length}，要自然真实，适合社交分享，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与分享主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n" +
                    "- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让朋友感受到真诚和价值\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeCtripReviewPrompt() {
        AdTypeConfigEntity config = getByTypeCode("ctrip_review");
        if (config != null) {
            config.setRequirements("- 要有详细的旅游体验和真实感受\n" +
                    "- 提供实用的旅游信息和专业建议\n" +
                    "- 要有具体的服务质量和设施描述\n" +
                    "- 突出性价比和预订建议\n" +
                    "- 要对其他旅客有实际参考价值\n" +
                    "- 包含交通、住宿、景点等全面信息\n" +
                    "- 语言要专业但易懂，有说服力");

            config.setStyle("专业实用、详细全面、参考价值高、旅游导向");

            config.setPromptTemplate("你是一位经验丰富的旅游达人，请为{platform}平台写一篇关于{keyword}的{content_type}。\n\n" +
                    "【旅游背景】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容标准】\n{requirements}\n\n" +
                    "【评价框架】\n1. 整体印象：对目的地或服务的总体评价\n" +
                    "2. 详细体验：住宿、交通、景点、服务等具体体验\n" +
                    "3. 实用信息：价格、预订、注意事项等实用建议\n" +
                    "4. 推荐指数：是否推荐及推荐理由\n\n" +
                    "【专业要点】\n- 提供详细的旅游攻略信息\n" +
                    "- 分析性价比和预订建议\n" +
                    "- 分享实用的旅游技巧和经验\n" +
                    "- 给出专业的旅游建议\n" +
                    "- 帮助其他旅客做出明智选择\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要突出旅游特色和体验）\n" +
                    "- 内容（content，{content_length}，要有专业性和实用价值，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与旅游主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n" +
                    "- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让其他旅客获得专业的旅游指导\n\n风格特点：{style}");

            this.updateById(config);
        }
    }

    private void optimizeDouyinReviewPrompt() {
        AdTypeConfigEntity config = getByTypeCode("douyin_review");
        if (config != null) {
            config.setRequirements("- 要有真实的消费体验和年轻化表达\n" +
                    "- 语言要活泼有趣，符合抖音用户习惯\n" +
                    "- 要有具体的产品和服务体验描述\n" +
                    "- 突出视觉效果和氛围感受\n" +
                    "- 要有网感和流行元素，容易传播\n" +
                    "- 提供实用的消费建议和避雷指南\n" +
                    "- 内容要有话题性，能引起互动");

            config.setStyle("年轻活泼、有网感、真实体验、话题性强");

            config.setPromptTemplate("你是一位年轻的抖音达人，请为{platform}平台写一篇关于{keyword}的{content_type}。\n\n" +
                    "【探店背景】\n关键词：{keyword}\n{title_section}\n\n" +
                    "【内容要求】\n{requirements}\n\n" +
                    "【评价重点】\n1. 第一印象：进店的第一感受和视觉冲击\n" +
                    "2. 产品体验：味道、质量、特色等具体描述\n" +
                    "3. 环境氛围：装修风格、拍照效果、氛围感\n" +
                    "4. 服务体验：服务态度、专业程度、贴心程度\n" +
                    "5. 性价比：价格合理性和推荐指数\n\n" +
                    "【语言特色】\n- 用年轻人喜欢的表达方式\n" +
                    "- 适当使用网络流行语和表情\n" +
                    "- 语言生动有趣，有画面感\n" +
                    "- 真实接地气，不装腔作势\n" +
                    "- 有个人特色和独特见解\n\n" +
                    "【输出格式】\n请以JSON格式返回以下内容（键名使用英文）：\n" +
                    "- 标题（title，{title_length}，要有吸引力和话题性）\n" +
                    "- 内容（content，{content_length}，要年轻化表达，有网感，不包含话题标签）\n" +
                    "- 话题（topics，{topics_count}个，{topics_format}，要与探店主题相关）\n\n" +
                    "【重要提醒】\n- content字段中不要包含话题标签\n" +
                    "- 所有话题标签都应该单独放在topics字段中\n" +
                    "- topics格式：{topics_format}\n" +
                    "- 内容要让年轻用户感受到真实和有趣\n\n风格特点：{style}");

            this.updateById(config);
        }
    }
}
