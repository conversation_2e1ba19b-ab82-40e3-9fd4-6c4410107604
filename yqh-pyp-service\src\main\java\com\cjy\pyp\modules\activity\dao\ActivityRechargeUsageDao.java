package com.cjy.pyp.modules.activity.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 充值使用记录DAO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@Mapper
public interface ActivityRechargeUsageDao extends BaseMapper<ActivityRechargeUsageEntity> {
    
    /**
     * 分页查询使用记录
     */
    List<ActivityRechargeUsageEntity> queryPage(Map<String, Object> params);
    
    /**
     * 查询用户使用统计
     */
    Map<String, Object> getUserUsageStats(@Param("userId") Long userId, @Param("activityId") Long activityId);
    
    /**
     * 查询用户已使用次数
     */
    Integer getUsedCountByUserAndActivity(@Param("userId") Long userId, @Param("activityId") Long activityId);

    /**
     * 查询活动的有效已使用次数（只统计来自有效充值记录的使用次数）
     */
    Integer getValidUsedCountByActivity(@Param("activityId") Long activityId);
}
