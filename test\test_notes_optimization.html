<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记类提示词优化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 笔记类提示词优化测试</h1>
        
        <div class="test-section">
            <h3>🚀 一键优化所有笔记类提示词</h3>
            <p>点击下面的按钮来应用所有笔记类提示词的优化配置</p>
            <button id="optimizeBtn" onclick="optimizeNotesPrompts()">优化笔记类提示词</button>
            <div id="optimizeResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 优化范围说明</h3>
            <div class="info result">
本次优化包括以下笔记类型：
✅ 小红书 - 图文笔记
✅ 大众点评 - 商户评价笔记  
✅ 美团点评 - 商户评价笔记
✅ 微信朋友圈 - 社交分享笔记
✅ 携程点评 - 旅游点评笔记
✅ 携程笔记 - 旅游笔记
✅ 抖音点评 - 商户点评笔记
✅ 通用文案 - 通用笔记

排除的类型（视频类）：
❌ 抖音 - 短视频类
❌ 快手 - 短视频类
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 优化效果</h3>
            <div class="info result">
优化后的提示词将具备以下特点：
• 更有真实感和个人特色
• 提供更多实用价值和可操作建议
• 语言更自然流畅，贴近用户习惯
• 减少二次编辑的需求
• 提高内容的直接可用性
• 增强内容的传播价值
• 放宽字数限制，生成更详细的内容 ⭐ 新增

字数限制优化：
📝 大众点评：150-300字（原100-200字）
📝 美团点评：150-300字（原100-200字）
📝 携程点评：200-400字（大幅提升）
📝 抖音点评：120-250字（原90字以内）
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试API连接</h3>
            <button onclick="testConnection()">测试后端连接</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 后端API基础URL（根据实际情况修改）
        const API_BASE_URL = 'http://localhost:8080/yqh-pyp-service';

        async function optimizeNotesPrompts() {
            const btn = document.getElementById('optimizeBtn');
            const result = document.getElementById('optimizeResult');
            
            btn.disabled = true;
            btn.textContent = '优化中...';
            result.style.display = 'block';
            result.className = 'result loading';
            result.textContent = '正在应用笔记类提示词优化，请稍候...';

            try {
                const response = await fetch(`${API_BASE_URL}/activity/adtypeconfig/optimizeNotesPrompts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    result.className = 'result success';
                    result.textContent = `✅ 优化成功！\n\n${data.message || '笔记类提示词优化完成，生成的内容将更有灵魂和个性化。'}\n\n优化时间：${new Date().toLocaleString()}`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ 优化失败：\n${data.msg || data.message || '未知错误'}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 网络错误：\n${error.message}\n\n请确保后端服务已启动并且URL配置正确。`;
            } finally {
                btn.disabled = false;
                btn.textContent = '优化笔记类提示词';
            }
        }

        async function testConnection() {
            const result = document.getElementById('connectionResult');
            result.style.display = 'block';
            result.className = 'result loading';
            result.textContent = '正在测试连接...';

            try {
                const response = await fetch(`${API_BASE_URL}/activity/adtypeconfig/enabled`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    result.className = 'result success';
                    result.textContent = `✅ 后端连接正常！\n\n当前配置的广告类型数量：${data.list ? data.list.length : 0}\n连接时间：${new Date().toLocaleString()}`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ 连接失败：HTTP ${response.status}\n请检查后端服务状态。`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 连接错误：\n${error.message}\n\n请确保：\n1. 后端服务已启动\n2. URL配置正确：${API_BASE_URL}\n3. 没有跨域限制`;
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('页面加载完成，API基础URL：', API_BASE_URL);
        };
    </script>
</body>
</html>
