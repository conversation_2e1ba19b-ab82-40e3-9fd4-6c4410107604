<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.activity.dao.ActivityRechargeUsageDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity" id="activityRechargeUsageMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="rechargeRecordId" column="recharge_record_id"/>
        <result property="usageType" column="usage_type"/>
        <result property="usageCount" column="usage_count"/>
        <result property="relatedId" column="related_id"/>
        <result property="description" column="description"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
    </resultMap>

    <select id="queryPage" resultType="com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity">
        SELECT 
            u.*,
            a.name as activityName
        FROM activity_recharge_usage u
        LEFT JOIN tb_activity a ON u.activity_id = a.id
        WHERE 1=1
        <if test="userId != null">
            AND u.user_id = #{userId}
        </if>
        <if test="activityId != null">
            AND u.activity_id = #{activityId}
        </if>
        <if test="usageType != null">
            AND u.usage_type = #{usageType}
        </if>
        <if test="appid != null and appid != ''">
            AND u.appid = #{appid}
        </if>
        ORDER BY u.create_on DESC
    </select>

    <select id="getUserUsageStats" resultType="java.util.Map">
        SELECT 
            COALESCE(SUM(usage_count), 0) as totalUsageCount,
            COUNT(*) as totalUsageRecords,
            COUNT(CASE WHEN usage_type = 1 THEN 1 END) as videoGenerationCount,
            COUNT(CASE WHEN usage_type = 2 THEN 1 END) as textGenerationCount
        FROM activity_recharge_usage 
        WHERE user_id = #{userId} 
        AND activity_id = #{activityId}
    </select>

    <select id="getUsedCountByUserAndActivity" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(usage_count), 0) as usedCount
        FROM activity_recharge_usage
        WHERE user_id = #{userId}
        AND activity_id = #{activityId}
    </select>

    <select id="getValidUsedCountByActivity" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(u.usage_count), 0) as validUsedCount
        FROM activity_recharge_usage u
        INNER JOIN activity_recharge_record r ON u.recharge_record_id = r.id
        WHERE u.activity_id = #{activityId}
        AND r.status = 1  -- 只统计已支付的充值记录
        AND (r.expire_time IS NULL OR r.expire_time > NOW())  -- 排除已过期的充值记录
    </select>

</mapper>
