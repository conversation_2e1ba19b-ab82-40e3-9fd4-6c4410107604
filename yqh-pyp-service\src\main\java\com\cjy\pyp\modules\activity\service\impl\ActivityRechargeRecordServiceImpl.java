package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.enume.RechargeStatusEnum;
import com.cjy.pyp.common.enume.RechargeTypeEnum;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.dao.ActivityRechargeRecordDao;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargePackageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRefundRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargePackageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService;
import com.cjy.pyp.modules.activity.service.ActivityRefundRecordService;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.vo.RechargeOrderVo;
import com.github.pagehelper.PageHelper;
import com.cjy.pyp.modules.activity.vo.CreateActivityPackageOrderVo;
import com.cjy.pyp.modules.activity.vo.AdminRechargeVo;
import com.cjy.pyp.modules.activity.enums.PackageTypeEnum;
import com.cjy.pyp.modules.activity.config.RechargeConfig;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionIntegrationService;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.wx.entity.WxAccount;
import com.cjy.pyp.modules.wx.service.WxAccountService;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeRefundResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.RandomStringUtils;
import java.util.Map;
import java.util.UUID;

/**
 * 充值记录服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@Service("activityRechargeRecordService")
public class ActivityRechargeRecordServiceImpl extends ServiceImpl<ActivityRechargeRecordDao, ActivityRechargeRecordEntity> 
        implements ActivityRechargeRecordService {

    @Autowired
    private ActivityRechargePackageService activityRechargePackageService;
    
    @Autowired
    private ActivityRechargeUsageService activityRechargeUsageService;
    
    @Autowired
    private ActivityService activityService;

    @Autowired
    private RechargeConfig rechargeConfig;

    @Autowired
    private SalesmanCommissionIntegrationService salesmanCommissionIntegrationService;

    @Autowired
    private WxUserSalesmanBindingService wxUserSalesmanBindingService;

    @Autowired
    private WxAccountService wxAccountService;

    @Autowired
    private ActivityRefundRecordService activityRefundRecordService;

    @Autowired
    private com.cjy.pyp.modules.channel.service.ChannelRefundPermissionService channelRefundPermissionService;

    @Override
    public List<ActivityRechargeRecordEntity> queryPage(Map<String, Object> params) {
        // 计算分页参数
        int page = Integer.parseInt(params.get("page").toString());
        int limit = Integer.parseInt(params.get("limit").toString());

        PageHelper.startPage(page,limit);
        List<ActivityRechargeRecordEntity> list = baseMapper.queryPage(params);

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createRechargeOrder(RechargeOrderVo rechargeOrderVo,Long userId) {
        // 验证活动是否存在
        ActivityEntity activity = activityService.getById(rechargeOrderVo.getActivityId());
        if (activity == null) {
            return R.error("活动不存在");
        }

        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(userId);
        record.setActivityId(rechargeOrderVo.getActivityId());
        record.setRechargeType(rechargeOrderVo.getRechargeType());
        record.setOrderSn(generateOrderSn());
        record.setStatus(RechargeStatusEnum.PENDING.getCode());
        record.setSource(1); // 用户充值
        record.setRemarks(rechargeOrderVo.getRemarks());
        record.setAppid(rechargeOrderVo.getAppid());

        // 检查用户是否绑定业务员，如果绑定则关联到订单
        try {
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(userId, rechargeOrderVo.getAppid());
            if (binding != null) {
                record.setSalesmanId(binding.getSalesmanId());
            }
        } catch (Exception e) {
            // 业务员绑定失败不影响订单创建
            System.err.println("获取业务员绑定信息失败: " + e.getMessage());
        }

        if (RechargeTypeEnum.PACKAGE.getCode().equals(rechargeOrderVo.getRechargeType())) {
            // 套餐充值
            if (rechargeOrderVo.getPackageId() == null) {
                return R.error("套餐ID不能为空");
            }
            
            ActivityRechargePackageEntity packageEntity = activityRechargePackageService.getById(rechargeOrderVo.getPackageId());
            if (packageEntity == null) {
                return R.error("套餐不存在");
            }
            if (packageEntity.getStatus() != 1) {
                return R.error("套餐已禁用");
            }
            if (!PackageTypeEnum.isRechargeCountPackage(packageEntity.getPackageType())) {
                return R.error("该套餐不是充值次数套餐，请使用创建活动套餐接口");
            }
            
            record.setPackageId(packageEntity.getId());
            record.setCountValue(packageEntity.getCountValue());
            record.setAmount(packageEntity.getPrice());
            
            // 设置过期时间
            if (packageEntity.getValidDays() != null) {
                LocalDateTime expireTime = LocalDateTime.now().plusDays(packageEntity.getValidDays());
                record.setExpireTime(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()));
            }
            
        } else if (RechargeTypeEnum.CUSTOM.getCode().equals(rechargeOrderVo.getRechargeType())) {
            // 自定义充值
            if (rechargeOrderVo.getCountValue() == null || rechargeOrderVo.getAmount() == null) {
                return R.error("充值次数和金额不能为空");
            }
            if (rechargeOrderVo.getCountValue() <= 0 || rechargeOrderVo.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return R.error("充值次数和金额必须大于0");
            }
            
            record.setCountValue(rechargeOrderVo.getCountValue());
            record.setAmount(rechargeOrderVo.getAmount());
        } else {
            return R.error("不支持的充值类型");
        }

        this.save(record);
        return R.ok().put("orderSn", record.getOrderSn()).put("orderId", record.getId());
    }

    /**
     * 生成订单号
     */
    private String generateOrderSn() {
        return "RC" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    @Override
    public ActivityRechargeRecordEntity findByOrderSn(String orderSn) {
        return baseMapper.findByOrderSn(orderSn);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R handlePaymentSuccess(String orderSn, String payTransaction, String payType) {
        ActivityRechargeRecordEntity record = findByOrderSn(orderSn);
        if (record == null) {
            return R.error("充值订单不存在");
        }

        if (RechargeStatusEnum.PAID.getCode().equals(record.getStatus())) {
            return R.error("订单已支付");
        }

        if (!RechargeStatusEnum.PENDING.getCode().equals(record.getStatus())) {
            return R.error("订单状态不支持支付");
        }

        // 检查是否为创建活动套餐
        if (RechargeTypeEnum.CREATE_ACTIVITY_PACKAGE.getCode().equals(record.getRechargeType())) {
            return handleActivityPackagePaymentSuccess(orderSn, payTransaction, payType);
        }

        // 更新订单状态
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setPayTransaction(payTransaction);
        record.setPayType(Integer.valueOf(payType));
        record.setPayAmount(record.getAmount());
        record.setPayTime(new Date());
        this.updateById(record);

        // 注意：不再直接更新activity.allCount，因为现在使用动态计算（只统计已支付且未过期的订单）

        // 触发佣金计算
        try {
            salesmanCommissionIntegrationService.handleRechargeCompleted(record.getId(), record.getAppid());
        } catch (Exception e) {
            // 佣金计算失败不影响主流程，只记录日志
            // logger.error("佣金计算失败: rechargeRecordId={}, orderSn={}", record.getId(), orderSn, e);
        }

        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R cancelRechargeOrder(Long id) {
        ActivityRechargeRecordEntity record = this.getById(id);
        if (record == null) {
            return R.error("充值订单不存在");
        }

        if (!RechargeStatusEnum.PENDING.getCode().equals(record.getStatus())) {
            return R.error("只能取消待支付的订单");
        }

        record.setStatus(RechargeStatusEnum.CANCELLED.getCode());
        this.updateById(record);

        return R.ok();
    }

    @Override
    public Integer getAvailableCountByUserAndActivity(Long userId, Long activityId) {
        return baseMapper.getAvailableCountByUserAndActivity(userId, activityId);
    }

    @Override
    public Map<String, Object> getUserRechargeStats(Long userId, Long activityId) {
        return baseMapper.getUserRechargeStats(userId, activityId);
    }

    @Override
    public Integer getPaidTotalCountByActivity(Long activityId) {
        QueryWrapper<ActivityRechargeRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        wrapper.eq("status", RechargeStatusEnum.PAID.getCode()); // 只统计已支付的订单
        // 排除已过期的订单：expire_time IS NULL（永不过期）或 expire_time > NOW()（未过期）
        wrapper.and(w -> w.isNull("expire_time").or().gt("expire_time", new Date()));

        List<ActivityRechargeRecordEntity> paidRecords = this.list(wrapper);

        return paidRecords.stream()
                .mapToInt(record -> record.getCountValue() != null ? record.getCountValue() : 0)
                .sum();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R giftCount(Long userId, Long activityId, Integer count, String remarks) {
        if (count <= 0) {
            return R.error("赠送次数必须大于0");
        }

        // 验证活动是否存在
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        // 创建赠送记录
        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(userId);
        record.setActivityId(activityId);
        record.setOrderSn(generateOrderSn());
        record.setRechargeType(RechargeTypeEnum.GIFT.getCode());
        record.setCountValue(count);
        record.setAmount(BigDecimal.ZERO);
        record.setPayAmount(BigDecimal.ZERO);
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setSource(2); // 系统赠送
        record.setRemarks(remarks);
        record.setPayTime(new Date());

        this.save(record);

        // 注意：不再直接更新activity.allCount，因为现在使用动态计算（只统计已支付且未过期的订单）

        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R checkAndDeductCount(Long userId, Long activityId, Integer usageType, Long relatedId, String description) {
        // 检查活动是否存在
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        // 根据使用类型获取需要消耗的次数
        Integer costCount = rechargeConfig.getCostByUsageType(usageType);
        
        // 获取用户可用的充值记录（按时间顺序，包含剩余次数）
        List<ActivityRechargeRecordEntity> availableRecords = baseMapper.getAvailableRechargeRecords(userId, activityId);

        // 计算总的可用次数
        int totalAvailable = availableRecords.stream()
                .mapToInt(record -> record.getRemainingCount() != null ? record.getRemainingCount() : 0)
                .sum();

        // 检查总的可用次数是否足够
        if (totalAvailable < costCount) {
            return R.error("可用次数不足，请先充值。需要消耗" + costCount + "次，剩余" + totalAvailable + "次");
        }

        // 按顺序从充值记录中扣减次数
        int remainingCost = costCount;
        for (ActivityRechargeRecordEntity record : availableRecords) {
            if (remainingCost <= 0) {
                break;
            }

            int availableInThisRecord = record.getRemainingCount() != null ? record.getRemainingCount() : 0;
            if (availableInThisRecord <= 0) {
                continue;
            }

            // 计算从这个记录中扣减的次数
            int deductFromThisRecord = Math.min(remainingCost, availableInThisRecord);

            // 记录使用（带appid参数，用于佣金计算）
            activityRechargeUsageService.recordUsage(userId, activityId, record.getId(),
                    usageType, deductFromThisRecord, relatedId, description, activity.getAppid());

            remainingCost -= deductFromThisRecord;
        }

        // 更新活动的总使用次数
        Integer currentUseCount = activity.getUseCount() != null ? activity.getUseCount() : 0;
        activity.setUseCount(currentUseCount + costCount);
        activityService.updateById(activity);

        // 如果是转发操作，触发转发佣金计算
        if (usageType != null && usageType == 3) {
            try {
                salesmanCommissionIntegrationService.handleForwardCompleted(userId, activityId, activity.getAppid());
            } catch (Exception e) {
                // 转发佣金计算失败不影响主流程，只记录日志
                System.err.println("转发佣金计算失败: userId=" + userId + ", activityId=" + activityId + ", error=" + e.getMessage());
            }
        }

        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createActivityPackageOrder(CreateActivityPackageOrderVo orderVo, Long userId) {
        // 验证套餐是否存在且为创建活动套餐
        ActivityRechargePackageEntity packageEntity = activityRechargePackageService.getById(orderVo.getPackageId());
        if (packageEntity == null) {
            return R.error("套餐不存在");
        }
        if (packageEntity.getStatus() != 1) {
            return R.error("套餐已禁用");
        }
        if (!PackageTypeEnum.isCreateActivityPackage(packageEntity.getPackageType())) {
            return R.error("该套餐不是创建活动套餐");
        }

        // 创建充值记录
        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(userId);
        record.setActivityId(null); // 创建活动套餐时活动ID为空，支付成功后填入
        record.setPackageId(orderVo.getPackageId());
        record.setOrderSn(generateOrderSn());
        record.setRechargeType(RechargeTypeEnum.CREATE_ACTIVITY_PACKAGE.getCode());
        record.setCountValue(packageEntity.getCountValue());
        record.setAmount(packageEntity.getPrice());
        record.setPayAmount(packageEntity.getPrice());
        record.setStatus(RechargeStatusEnum.PENDING.getCode());
        record.setSource(1); // 用户充值
        record.setRemarks(orderVo.getRemarks());
        record.setAppid(orderVo.getAppid());
        record.setActivityTemplateId(orderVo.getActivityTemplateId());
        record.setActivityNameForCreation(orderVo.getActivityName());

        this.save(record);
        return R.ok().put("orderSn", record.getOrderSn()).put("orderId", record.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R handleActivityPackagePaymentSuccess(String orderSn, String payTransaction, String payType) {
        // 查找订单
        ActivityRechargeRecordEntity record = findByOrderSn(orderSn);
        if (record == null) {
            return R.error("订单不存在");
        }

        if (!RechargeTypeEnum.CREATE_ACTIVITY_PACKAGE.getCode().equals(record.getRechargeType())) {
            return R.error("订单类型错误");
        }

        if (RechargeStatusEnum.PAID.getCode().equals(record.getStatus())) {
            return R.ok("订单已处理");
        }

        // 更新订单状态
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setPayTransaction(payTransaction);
        record.setPayType(Integer.valueOf(payType));
        record.setPayTime(new Date());

        // 创建活动
        ActivityEntity newActivity = createActivityFromTemplate(record);
        if (newActivity == null) {
            return R.error("创建活动失败");
        }

        // 更新订单中的活动ID
        record.setActivityId(newActivity.getId());
        record.setCreatedActivityId(newActivity.getId());

        this.updateById(record);

        // 触发佣金计算
        try {
            salesmanCommissionIntegrationService.handleRechargeCompleted(record.getId(), record.getAppid());
        } catch (Exception e) {
            // 佣金计算失败不影响主流程，只记录日志
            // logger.error("创建活动套餐佣金计算失败: rechargeRecordId={}, activityId={}", record.getId(), newActivity.getId(), e);
        }

        return R.ok().put("activityId", newActivity.getId());
    }

    /**
     * 根据模板创建活动
     */
    private ActivityEntity createActivityFromTemplate(ActivityRechargeRecordEntity record) {
        try {
            ActivityEntity newActivity = new ActivityEntity();
            newActivity.setName(record.getActivityNameForCreation());
            newActivity.setCode(RandomStringUtils.randomAlphanumeric(10)); // 生成10位随机码
            newActivity.setAllCount(record.getCountValue());
            newActivity.setUseCount(0);
            newActivity.setAppid(record.getAppid());
            newActivity.setCreateBy(record.getUserId());

            // 设置默认值
            if (newActivity.getType() == null) {
                newActivity.setType("0"); // 默认自定义类型
            }

            activityService.save(newActivity);
            return newActivity;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R adminRecharge(AdminRechargeVo rechargeVo, Long operatorId) {
        // 验证活动是否存在
        ActivityEntity activity = activityService.getById(rechargeVo.getActivityId());
        if (activity == null) {
            return R.error("活动不存在");
        }

        if (rechargeVo.getRechargeType() == 1) {
            // 套餐充值
            return handlePackageRecharge(rechargeVo, activity, operatorId);
        } else if (rechargeVo.getRechargeType() == 3) {
            // 系统赠送
            return handleSystemGift(rechargeVo, activity, operatorId);
        } else {
            return R.error("不支持的充值方式");
        }
    }

    /**
     * 处理套餐充值
     */
    private R handlePackageRecharge(AdminRechargeVo rechargeVo, ActivityEntity activity, Long operatorId) {
        if (rechargeVo.getPackageId() == null) {
            return R.error("套餐ID不能为空");
        }

        // 验证套餐
        ActivityRechargePackageEntity packageEntity = activityRechargePackageService.getById(rechargeVo.getPackageId());
        if (packageEntity == null) {
            return R.error("套餐不存在");
        }
        if (packageEntity.getStatus() != 1) {
            return R.error("套餐已禁用");
        }

        // 检查套餐类型，只允许充值次数套餐
        if (!PackageTypeEnum.isRechargeCountPackage(packageEntity.getPackageType())) {
            return R.error("该套餐不是充值次数套餐");
        }

        // 创建充值记录
        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(activity.getCreateBy()); // 使用活动创建者作为用户ID
        record.setActivityId(rechargeVo.getActivityId());
        record.setPackageId(rechargeVo.getPackageId());
        record.setOrderSn(generateOrderSn());
        record.setRechargeType(RechargeTypeEnum.PACKAGE.getCode());
        record.setCountValue(packageEntity.getCountValue());
        record.setAmount(packageEntity.getPrice());
        record.setPayAmount(packageEntity.getPrice());
        record.setStatus(RechargeStatusEnum.PAID.getCode()); // 管理端直接设为已支付
        record.setSource(2); // 系统赠送
        record.setRemarks(rechargeVo.getRemarks());
        record.setAppid(rechargeVo.getAppid());
        record.setPayTime(new Date());
        record.setCreateBy(operatorId);

        // 设置过期时间
        if (rechargeVo.getValidDays() != null && rechargeVo.getValidDays() > 0) {
            LocalDateTime expireTime = LocalDateTime.now().plusDays(rechargeVo.getValidDays());
            record.setExpireTime(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()));
        } else if (packageEntity.getValidDays() != null) {
            LocalDateTime expireTime = LocalDateTime.now().plusDays(packageEntity.getValidDays());
            record.setExpireTime(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        this.save(record);

        // 注意：不再直接更新activity.allCount，因为现在使用动态计算（只统计已支付且未过期的订单）

        return R.ok().put("message", "套餐充值成功").put("addedCount", packageEntity.getCountValue());
    }

    /**
     * 处理系统赠送
     */
    private R handleSystemGift(AdminRechargeVo rechargeVo, ActivityEntity activity, Long operatorId) {
        if (rechargeVo.getCount() == null || rechargeVo.getCount() <= 0) {
            return R.error("赠送次数必须大于0");
        }

        // 创建赠送记录
        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(activity.getCreateBy()); // 使用活动创建者作为用户ID
        record.setActivityId(rechargeVo.getActivityId());
        record.setOrderSn(generateOrderSn());
        record.setRechargeType(RechargeTypeEnum.GIFT.getCode());
        record.setCountValue(rechargeVo.getCount());
        record.setAmount(BigDecimal.ZERO);
        record.setPayAmount(BigDecimal.ZERO);
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setSource(2); // 系统赠送
        record.setRemarks(rechargeVo.getRemarks());
        record.setAppid(rechargeVo.getAppid());
        record.setPayTime(new Date());
        record.setCreateBy(operatorId);

        // 设置过期时间
        if (rechargeVo.getValidDays() != null && rechargeVo.getValidDays() > 0) {
            LocalDateTime expireTime = LocalDateTime.now().plusDays(rechargeVo.getValidDays());
            record.setExpireTime(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        this.save(record);

        // 注意：不再直接更新activity.allCount，因为现在使用动态计算（只统计已支付且未过期的订单）

        return R.ok().put("message", "系统赠送成功").put("addedCount", rechargeVo.getCount());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R giftCountWithValidDays(Long activityId, Integer count, Integer validDays, String remarks,String appid, Long operatorId) {
        if (count <= 0) {
            return R.error("赠送次数必须大于0");
        }

        // 验证活动是否存在
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        // 创建赠送记录
        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(activity.getCreateBy()); // 使用活动创建者作为用户ID
        record.setActivityId(activityId);
        record.setOrderSn(generateOrderSn());
        record.setRechargeType(RechargeTypeEnum.GIFT.getCode());
        record.setCountValue(count);
        record.setAmount(BigDecimal.ZERO);
        record.setPayAmount(BigDecimal.ZERO);
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setSource(2); // 系统赠送
        record.setRemarks(remarks);
        record.setPayTime(new Date());
        record.setAppid(appid);
        record.setCreateBy(operatorId);

        // 设置过期时间
        if (validDays != null && validDays > 0) {
            LocalDateTime expireTime = LocalDateTime.now().plusDays(validDays);
            record.setExpireTime(Date.from(expireTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        this.save(record);

        // 注意：不再直接更新activity.allCount，因为现在使用动态计算（只统计已支付且未过期的订单）

        return R.ok().put("message", "系统赠送成功").put("addedCount", count);
    }

    @Override
    public List<ActivityRechargeRecordEntity> querySalesmanOrderPage(Map<String, Object> params) {
        // 使用自定义SQL查询
        // 分页处理
        int pageNum = Integer.parseInt(params.getOrDefault("page", "1").toString());
        int pageSize = Integer.parseInt(params.getOrDefault("limit", "10").toString());
        PageHelper.startPage(pageNum, pageSize);
        List<ActivityRechargeRecordEntity> list = baseMapper.selectSalesmanOrderPage(params);


        return list;
    }

    @Override
    public Map<String, Object> getSalesmanOrderStats(Map<String, Object> params) {
        return baseMapper.selectSalesmanOrderStats(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R applyRefund(Long rechargeRecordId, String orderSn, Long userId) {
        // 验证充值记录是否存在
        ActivityRechargeRecordEntity record = this.getById(rechargeRecordId);
        if (record == null) {
            return R.error("充值记录不存在");
        }

        // 验证订单号是否匹配
        if (!orderSn.equals(record.getOrderSn())) {
            return R.error("订单号不匹配");
        }

        // 验证是否为当前用户的记录
        if (!userId.equals(record.getUserId())) {
            return R.error("无权操作此订单");
        }

        // 验证订单状态（只有已支付的订单才能退款）
        if (!RechargeStatusEnum.PAID.getCode().equals(record.getStatus())) {
            return R.error("只有已支付的订单才能申请退款");
        }

        // 新增：检查渠道退款权限
        if (!channelRefundPermissionService.hasRefundPermission(rechargeRecordId)) {
            String reason = channelRefundPermissionService.getRefundPermissionDeniedReason(rechargeRecordId);
            return R.error("该订单不具备退款权限：" + reason);
        }

        // 验证充值类型（只有用户自己充值的才能退款）
        if (!RechargeTypeEnum.PACKAGE.getCode().equals(record.getRechargeType()) &&
            !RechargeTypeEnum.CUSTOM.getCode().equals(record.getRechargeType())) {
            return R.error("该类型的充值记录不支持退款");
        }

        // 验证充值来源（只有用户充值的才能退款，排除系统赠送和活动奖励）
        if (record.getSource() != null && !record.getSource().equals(1)) {
            return R.error("系统赠送和活动奖励的充值记录不支持退款");
        }

        // 获取已使用次数（需要重新查询以获取最新的使用情况）
        List<ActivityRechargeRecordEntity> records = baseMapper.getAvailableRechargeRecords(userId, record.getActivityId());
        ActivityRechargeRecordEntity currentRecord = records.stream()
                .filter(r -> r.getId().equals(rechargeRecordId))
                .findFirst()
                .orElse(null);

        if (currentRecord == null) {
            return R.error("无法获取充值记录的使用情况");
        }

        Integer usedCount = currentRecord.getUsedCount() != null ? currentRecord.getUsedCount() : 0;
        Integer totalCount = record.getCountValue() != null ? record.getCountValue() : 0;
        Integer remainingCount = totalCount - usedCount;

        // 如果已经全部使用完，不能退款
        if (remainingCount <= 0) {
            return R.error("该充值记录已全部使用完，无法退款");
        }

        // 计算退款金额（按剩余次数比例计算）
        BigDecimal originalAmount = record.getPayAmount() != null ? record.getPayAmount() : record.getAmount();
        BigDecimal refundAmount;

        if (usedCount == 0) {
            // 未使用，全额退款
            refundAmount = originalAmount;
        } else {
            // 按剩余次数比例退款
            refundAmount = originalAmount.multiply(new BigDecimal(remainingCount))
                    .divide(new BigDecimal(totalCount), 2, BigDecimal.ROUND_HALF_UP);
        }

        // 更新订单状态为退款中
        record.setStatus(RechargeStatusEnum.REFUNDING.getCode());
        record.setRefundAmount(refundAmount);
        record.setRefundTime(new Date());
        this.updateById(record);

        // 创建退款记录
        ActivityRefundRecordEntity refundRecord = new ActivityRefundRecordEntity();
        refundRecord.setRechargeRecordId(record.getId());
        refundRecord.setOriginalOrderSn(record.getOrderSn());
        refundRecord.setRefundOrderSn("RF" + record.getOrderSn() + System.currentTimeMillis());
        refundRecord.setUserId(userId);
        refundRecord.setActivityId(record.getActivityId());
        refundRecord.setOriginalAmount(record.getPayAmount() != null ? record.getPayAmount() : record.getAmount());
        refundRecord.setRefundAmount(refundAmount);
        refundRecord.setPayType(record.getPayType());
        refundRecord.setStatus(0); // 申请中
        refundRecord.setRefundReason("用户申请退款");
        refundRecord.setApplyTime(new Date());
        refundRecord.setCreateTime(new Date());
        refundRecord.setUpdateTime(new Date());

        // 保存退款记录
        activityRefundRecordService.createRefundRecord(refundRecord);

        return R.ok().put("refundAmount", refundAmount).put("message", "退款申请已提交，预计退款金额：¥" + refundAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R approveRefund(Long id, String orderSn, Long operatorId) {
        // 验证充值记录是否存在
        ActivityRechargeRecordEntity record = this.getById(id);
        if (record == null) {
            return R.error("充值记录不存在");
        }

        // 验证订单号是否匹配
        if (!orderSn.equals(record.getOrderSn())) {
            return R.error("订单号不匹配");
        }

        // 验证订单状态（只有退款中的订单才能审核）
        if (!RechargeStatusEnum.REFUNDING.getCode().equals(record.getStatus())) {
            return R.error("只有退款中的订单才能审核");
        }

        // 执行实际退款
        R refundResult = processActualRefund(record);
        if (!refundResult.get("code").equals(200)) {
            // 退款失败，更新退款记录状态
            ActivityRefundRecordEntity refundRecord = activityRefundRecordService.getByRechargeRecordId(record.getId());
            if (refundRecord != null) {
                refundRecord.setStatus(3); // 退款失败
                refundRecord.setAuditTime(new Date());
                refundRecord.setAuditBy(operatorId);
                refundRecord.setRemarks("退款失败：" + refundResult.get("msg"));
                refundRecord.setUpdateTime(new Date());
                activityRefundRecordService.updateById(refundRecord);
            }
            return refundResult;
        }

        // 退款成功，更新退款记录的第三方交易号
        ActivityRefundRecordEntity refundRecord = activityRefundRecordService.getByRechargeRecordId(record.getId());
        if (refundRecord != null && refundResult.get("refundId") != null) {
            refundRecord.setRefundTransactionId(refundResult.get("refundId").toString());
            refundRecord.setUpdateTime(new Date());
            activityRefundRecordService.updateById(refundRecord);
        }

        // 更新订单状态为已退款
        record.setStatus(RechargeStatusEnum.REFUNDED.getCode());
        record.setUpdateBy(operatorId);
        this.updateById(record);

        // 释放退款权限（退款成功后）
        try {
            channelRefundPermissionService.releaseRefundPermission(record.getId());
        } catch (Exception e) {
            // 权限释放失败不影响退款流程，只记录日志
            System.err.println("释放退款权限失败：" + e.getMessage());
        }

        // 更新退款记录状态为退款成功
        ActivityRefundRecordEntity finalRefundRecord = activityRefundRecordService.getByRechargeRecordId(record.getId());
        if (finalRefundRecord != null) {
            finalRefundRecord.setStatus(2); // 退款成功
            finalRefundRecord.setAuditTime(new Date());
            finalRefundRecord.setRefundTime(new Date());
            finalRefundRecord.setAuditBy(operatorId);
            finalRefundRecord.setUpdateTime(new Date());
            activityRefundRecordService.updateById(finalRefundRecord);
        }

        // 更新活动的总次数（减去退款的次数）
        ActivityEntity activity = activityService.getById(record.getActivityId());
        if (activity != null) {
            Integer currentAllCount = activity.getAllCount() != null ? activity.getAllCount() : 0;
            Integer refundCount = record.getCountValue() != null ? record.getCountValue() : 0;
            activity.setAllCount(Math.max(0, currentAllCount - refundCount));
            activityService.updateById(activity);
        }

        return R.ok().put("message", "退款审核通过，退款处理成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R rejectRefund(Long id, String orderSn, String rejectReason, Long operatorId) {
        // 验证充值记录是否存在
        ActivityRechargeRecordEntity record = this.getById(id);
        if (record == null) {
            return R.error("充值记录不存在");
        }

        // 验证订单号是否匹配
        if (!orderSn.equals(record.getOrderSn())) {
            return R.error("订单号不匹配");
        }

        // 验证订单状态（只有退款中的订单才能审核）
        if (!RechargeStatusEnum.REFUNDING.getCode().equals(record.getStatus())) {
            return R.error("只有退款中的订单才能审核");
        }

        // 更新订单状态为已支付（恢复原状态）
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setRefundAmount(null);
        record.setRefundTime(null);
        record.setRemarks(record.getRemarks() + " [退款被拒绝：" + rejectReason + "]");
        record.setUpdateBy(operatorId);
        this.updateById(record);

        // 更新退款记录状态为审核拒绝
        ActivityRefundRecordEntity refundRecord = activityRefundRecordService.getByRechargeRecordId(record.getId());
        if (refundRecord != null) {
            refundRecord.setStatus(4); // 审核拒绝
            refundRecord.setAuditTime(new Date());
            refundRecord.setAuditBy(operatorId);
            refundRecord.setRejectReason(rejectReason);
            refundRecord.setUpdateTime(new Date());
            activityRefundRecordService.updateById(refundRecord);
        }

        return R.ok().put("message", "已拒绝退款申请");
    }

    /**
     * 处理实际退款
     */
    private R processActualRefund(ActivityRechargeRecordEntity record) {
        try {
            // 根据支付方式调用相应的退款接口
            Integer payType = record.getPayType();
            BigDecimal originalAmount = record.getPayAmount();
            BigDecimal refundAmount = record.getRefundAmount();
            String orderSn = record.getOrderSn();
            String payTransaction = record.getPayTransaction();

            if (payType == null) {
                return R.error("支付方式信息缺失，无法退款");
            }

            if (payType == 1) {
                // 微信支付退款
                return processWechatRefund(orderSn, payTransaction,originalAmount, refundAmount, record.getAppid());
            } else if (payType == 2) {
                // 支付宝退款
                return processAlipayRefund(orderSn, payTransaction, refundAmount, record.getAppid());
            } else {
                return R.error("不支持的支付方式");
            }
        } catch (Exception e) {
            return R.error("退款处理失败：" + e.getMessage());
        }
    }

    /**
     * 处理微信支付退款
     */
    private R processWechatRefund(String orderSn, String payTransaction, BigDecimal originalAmount, BigDecimal refundAmount, String appid) {
        try {
            // 1. 获取微信支付配置
            WxAccount wxAccount = wxAccountService.findByAppid(appid);
            if (wxAccount == null) {
                return R.error("微信支付配置不存在");
            }

            // 2. 构建微信支付配置
            WxPayConfig payConfig = new WxPayConfig();
            payConfig.setAppId(wxAccount.getAppid());
            payConfig.setMchId(wxAccount.getMchId());
            payConfig.setMchKey(wxAccount.getMchKey());
            payConfig.setKeyPath(wxAccount.getKeyPath());
            payConfig.setTradeType("JSAPI");
            payConfig.setSignType("MD5");

            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(payConfig);

            // 3. 构建退款请求参数
            WxPayRefundRequest refundRequest = new WxPayRefundRequest();
            refundRequest.setOutTradeNo(orderSn);
            refundRequest.setOutRefundNo("RF" + orderSn + System.currentTimeMillis()); // 退款单号
            refundRequest.setTotalFee(originalAmount.multiply(new BigDecimal(100)).intValue()); // 原订单金额（分）
            refundRequest.setRefundFee(refundAmount.multiply(new BigDecimal(100)).intValue()); // 退款金额（分）
            refundRequest.setRefundDesc("用户申请退款");

            // 4. 调用微信退款接口
            WxPayRefundResult refundResult = wxPayService.refund(refundRequest);

            if ("SUCCESS".equals(refundResult.getReturnCode()) && "SUCCESS".equals(refundResult.getResultCode())) {
                return R.ok().put("message", "微信退款处理成功").put("refundId", refundResult.getRefundId());
            } else {
                return R.error("微信退款失败：" + refundResult.getReturnMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("微信退款处理异常：" + e.getMessage());
        }
    }

    /**
     * 处理支付宝退款
     */
    private R processAlipayRefund(String orderSn, String payTransaction, BigDecimal refundAmount, String appid) {
        try {
            // 1. 获取支付宝配置
            WxAccount wxAccount = wxAccountService.findByAppid(appid);
            if (wxAccount == null || wxAccount.getAliAppId() == null) {
                return R.error("支付宝配置不存在");
            }

            // 2. 构建支付宝配置
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setAppId(wxAccount.getAliAppId());
            alipayConfig.setPrivateKey(wxAccount.getAliAppPrivateKey());
            alipayConfig.setAlipayPublicKey(wxAccount.getAliAppPublicKey());

            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);

            // 3. 构建退款请求参数
            AlipayTradeRefundRequest refundRequest = new AlipayTradeRefundRequest();
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();
            model.setOutTradeNo(orderSn);
            model.setRefundAmount(refundAmount.toString());
            model.setRefundReason("用户申请退款");
            model.setOutRequestNo("RF" + orderSn + System.currentTimeMillis()); // 退款请求号

            refundRequest.setBizModel(model);

            // 4. 调用支付宝退款接口
            AlipayTradeRefundResponse refundResponse = alipayClient.execute(refundRequest);

            if (refundResponse.isSuccess()) {
                return R.ok().put("message", "支付宝退款处理成功").put("refundId", refundResponse.getOutTradeNo());
            } else {
                return R.error("支付宝退款失败：" + refundResponse.getSubMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("支付宝退款处理异常：" + e.getMessage());
        }
    }
}
