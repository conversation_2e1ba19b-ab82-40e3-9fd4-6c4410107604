package com.cjy.pyp.modules.activity.controller;
import java.text.ParseException;
import java.util.*;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.DateUtils;
import com.cjy.pyp.modules.activity.entity.ActivityBottomEntity;
import com.cjy.pyp.modules.activity.service.ActivityBottomService;
import com.cjy.pyp.modules.activity.service.ActivityConfigService;
import com.cjy.pyp.modules.activity.vo.ActivityDateVo;
import com.cjy.pyp.modules.activity.vo.ActivitySimpleVo;
import com.cjy.pyp.modules.activity.vo.OssImageVo;
import com.cjy.pyp.modules.cms.service.CmsService;
import com.cjy.pyp.modules.wx.service.MsgReplyRuleService;
import com.cjy.pyp.modules.wx.service.WxQrCodeService;
import com.cjy.pyp.modules.channel.utils.ChannelDataUtils;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-06-02 23:19:20
 */
@RestController
@RequestMapping("/activity/activity")
public class ActivityController extends AbstractController {
    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ActivityService activityService;
    @Autowired
    private CmsService cmsService;
    @Autowired
    private WxQrCodeService wxQrCodeService;
    @Autowired
    private MsgReplyRuleService msgReplyRuleService;
    @Autowired
    private ActivityBottomService  activityBottomService;
    @Autowired
    private ActivityConfigService activityConfigService;
    @Autowired
    private ChannelDataUtils channelDataUtils;
    @Autowired
    private com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService activityRechargeRecordService;
    @Autowired
    private com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService activityRechargeUsageService;

    @GetMapping("copy")
    @ApiOperation(value = "复制会议")
    @SysLog("复制会议")
    public R copy(@RequestParam("id") Long id) {
        activityService.copy(id);
        return R.ok();
    }
    @GetMapping("findByAppid")
    @ApiOperation(value = "通过appid获取会议(简要信息)")
    public R findByAppid(@CookieValue String appid) {
        List<ActivityEntity> activitySimpleVos = activityService.findByAppid(appid);
        return R.ok().put("result",activitySimpleVos);
    }

    @PostMapping("updateIsFirstChar")
    public R updateIsFirstChar(@RequestBody ActivityEntity activity) {
        activityService.updateById(activity);
        return R.ok();
    }


    @RequestMapping("/dateBetween/{id}")
    @ApiOperation(value = "获取会议日期")
    public R dateBetween(@PathVariable("id") Long id) throws ParseException {
        ActivityEntity activityEntity = activityService.getById(id);
        // 日期返回
        List<ActivityDateVo> activityDateVos = new ArrayList<>();
        // 只获取日期
        Date startDate = DateUtils.returnOnlyDate(activityEntity.getStartTime());
        Date endDate = DateUtils.returnOnlyDate(activityEntity.getEndTime());
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();
        long oneDay = 1000 * 60 * 60 * 24L;
        long time = startTime;
        while (time <= endTime) {
            Date date = new Date(time);
            ActivityDateVo activityDateVo = new ActivityDateVo();
            activityDateVo.setTextDate(DateUtils.format(date,DateUtils.DATE_PATTERN_MMDD));
            activityDateVo.setName(DateUtils.format(date,DateUtils.DATE_PATTERN_MMDD));
            activityDateVo.setRealDate(DateUtils.format(date,DateUtils.DATE_TIME_PATTERN_OTHER));
            activityDateVos.add(activityDateVo);
            time += oneDay;
        }
        return R.ok().put("result",activityDateVos);
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
//    @RequiresPermissions("activity:activity:list")
    public R list(@RequestParam Map<String, Object> params,@CookieValue String appid) {
        params.put("appid",appid);

        // 应用渠道权限过滤
        Long userId = getUser().getUserId();
        java.util.List<Long> accessibleChannelIds = channelDataUtils.getAccessibleChannelIds(userId);
        if (accessibleChannelIds != null && !accessibleChannelIds.isEmpty()) {
            params.put("channelIds", accessibleChannelIds);
        }

        PageUtils page = activityService.queryPage(params);

        // 更新活动列表中的统计信息，只统计已支付且未过期的订单
        if (page.getList() != null && !page.getList().isEmpty()) {
            java.util.List<ActivityEntity> activities = (java.util.List<ActivityEntity>) page.getList();
            for (ActivityEntity activity : activities) {
                // 获取已支付且未过期订单的总次数
                Integer paidTotalCount = activityRechargeRecordService.getPaidTotalCountByActivity(activity.getId());
                activity.setAllCount(paidTotalCount);

                // 获取有效的已使用次数（只统计来自有效充值记录的使用次数）
                Integer validUsedCount = activityRechargeUsageService.getValidUsedCountByActivity(activity.getId());
                activity.setUseCount(validUsedCount);
            }
        }

        return R.ok().put("page", page);
    }


    @RequestMapping("/listExtra")
//    @RequiresPermissions("activity:activity:list")
    public R listExtra(@RequestParam Map<String, Object> params,@CookieValue String appid) {
        params.put("appid",appid);
        PageUtils page = activityService.queryPage(params);

        // 更新活动列表中的统计信息，只统计已支付且未过期的订单
        if (page.getList() != null && !page.getList().isEmpty()) {
            java.util.List<ActivityEntity> activities = (java.util.List<ActivityEntity>) page.getList();
            for (ActivityEntity activity : activities) {
                // 获取已支付且未过期订单的总次数
                Integer paidTotalCount = activityRechargeRecordService.getPaidTotalCountByActivity(activity.getId());
                activity.setAllCount(paidTotalCount);

                // 获取有效的已使用次数（只统计来自有效充值记录的使用次数）
                Integer validUsedCount = activityRechargeUsageService.getValidUsedCountByActivity(activity.getId());
                activity.setUseCount(validUsedCount);
            }
        }

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
//    @RequiresPermissions("activity:activity:info")
    public R info(@PathVariable("id") Long id) {
        logger.error(String.valueOf(id));
        ActivityEntity activity = activityService.getById(id);
        if (activity.getMobileBanner() != null && StringUtils.isNotBlank(activity.getMobileBanner())) {
            String[] temp = activity.getMobileBanner().replaceAll("，", ",").split(",");
            List<OssImageVo> ossImageVos = new ArrayList<>();
            for (String s : temp) {
                OssImageVo ossImageVo = new OssImageVo();
                ossImageVo.setUrl(s);
                ossImageVos.add(ossImageVo);
            }
            activity.setAppFileList(ossImageVos);
        }
        if (activity.getPcBanner() != null && StringUtils.isNotBlank(activity.getPcBanner())) {
            String[] temp = activity.getPcBanner().replaceAll("，", ",").split(",");
            List<OssImageVo> ossImageVos = new ArrayList<>();
            for (String s : temp) {
                OssImageVo ossImageVo = new OssImageVo();
                ossImageVo.setUrl(s);
                ossImageVos.add(ossImageVo);
            }
            activity.setFileList(ossImageVos);
        }

        // 获取已支付且未过期订单的总次数
        Integer paidTotalCount = activityRechargeRecordService.getPaidTotalCountByActivity(id);
        activity.setAllCount(paidTotalCount);

        // 获取有效的已使用次数（只统计来自有效充值记录的使用次数）
        Integer validUsedCount = activityRechargeUsageService.getValidUsedCountByActivity(id);
        activity.setUseCount(validUsedCount);

        return R.ok().put("activity", activity);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("activity:activity:save")
    public R save(@RequestBody ActivityEntity activity,@CookieValue String appid) throws WxErrorException {
        // 设置appid
        activity.setAppid(appid);
        // 生成10位随机码
        activity.setCode(RandomStringUtils.randomAlphanumeric(10));

        // 自动设置渠道ID：根据创建者的业务员绑定关系确定渠道归属
        Long userId = getUser().getUserId();
        channelDataUtils.setActivityChannelId(activity, userId, appid);

        // 1.先保存活动表
        activityService.save(activity);
        // 2.再保存建站数据
        // cmsService.insertDefaultCms(activity.getId());
        // 3.生成公众号会议二维码
        // wxQrCodeService.fastCreateQrCode(activity,appid);
        // 4.如果是福建时代，新增一个跳转
        if (appid.equals("wx0770d56458b33c67")) {
            ActivityBottomEntity activityBottomEntity = new ActivityBottomEntity();
            activityBottomEntity.setActivityId(activity.getId());
            activityBottomEntity.setName("会务支持：时代会务");
            activityBottomEntity.setType(0);
            activityBottomEntity.setUrl("http://fjmeeting.com/mp_fjsd/#/");
            activityBottomService.save(activityBottomEntity);
        }
        // 5.创建会议配置表
        activityConfigService.init(activity.getId());
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("activity:activity:update")
    public R update(@RequestBody ActivityEntity activity,@CookieValue String appid) {
        activityService.updateById(activity);
        // 更新公众号自动回复规则
        // msgReplyRuleService.saveOrUpdateActivity(activity,appid);
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("activity:activity:delete")
    public R delete(@RequestBody Long[] ids) {
        activityService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

}
