package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.vo.RechargeOrderVo;
import com.cjy.pyp.modules.activity.vo.CreateActivityPackageOrderVo;
import com.cjy.pyp.modules.activity.vo.AdminRechargeVo;

import java.util.List;
import java.util.Map;

/**
 * 充值记录服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
public interface ActivityRechargeRecordService extends IService<ActivityRechargeRecordEntity> {

    /**
     * 分页查询
     */
    List<ActivityRechargeRecordEntity> queryPage(Map<String, Object> params);

    /**
     * 创建充值订单
     */
    R createRechargeOrder(RechargeOrderVo rechargeOrderVo,Long userId);

    /**
     * 根据订单号查询充值记录
     */
    ActivityRechargeRecordEntity findByOrderSn(String orderSn);

    /**
     * 处理充值支付成功回调
     */
    R handlePaymentSuccess(String orderSn, String payTransaction, String payType);

    /**
     * 取消充值订单
     */
    R cancelRechargeOrder(Long id);

    /**
     * 查询用户在指定活动的可用充值次数
     */
    Integer getAvailableCountByUserAndActivity(Long userId, Long activityId);

    /**
     * 查询用户充值统计
     */
    Map<String, Object> getUserRechargeStats(Long userId, Long activityId);

    /**
     * 系统赠送次数
     */
    R giftCount(Long userId, Long activityId, Integer count, String remarks);

    /**
     * 检查并扣减使用次数
     */
    R checkAndDeductCount(Long userId, Long activityId, Integer usageType, Long relatedId, String description);

    /**
     * 创建活动套餐订单
     */
    R createActivityPackageOrder(CreateActivityPackageOrderVo orderVo, Long userId);

    /**
     * 处理创建活动套餐支付成功回调
     */
    R handleActivityPackagePaymentSuccess(String orderSn, String payTransaction, String payType);

    /**
     * 管理端充值（支持套餐充值和系统赠送）
     */
    R adminRecharge(AdminRechargeVo rechargeVo, Long operatorId);

    /**
     * 申请退款
     */
    R applyRefund(Long rechargeRecordId, String orderSn, Long userId);

    /**
     * 获取活动的已支付订单总次数
     */
    Integer getPaidTotalCountByActivity(Long activityId);

    /**
     * 审核通过退款
     */
    R approveRefund(Long id, String orderSn, Long operatorId);

    /**
     * 拒绝退款
     */
    R rejectRefund(Long id, String orderSn, String rejectReason, Long operatorId);

    /**
     * 系统赠送次数（支持有效期设置）
     */
    R giftCountWithValidDays(Long activityId, Integer count, Integer validDays, String remarks,String appid, Long operatorId);

    /**
     * 分页查询业务员订单列表
     */
    List<ActivityRechargeRecordEntity> querySalesmanOrderPage(Map<String, Object> params);

    /**
     * 查询业务员订单统计
     */
    Map<String, Object> getSalesmanOrderStats(Map<String, Object> params);
}
