package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity;

import java.util.Map;

/**
 * 充值使用记录服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
public interface ActivityRechargeUsageService extends IService<ActivityRechargeUsageEntity> {

    /**
     * 分页查询
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 记录使用
     */
    void recordUsage(Long userId, Long activityId, Long rechargeRecordId, Integer usageType,
                    Integer usageCount, Long relatedId, String description);

    /**
     * 记录使用（带appid参数，用于佣金计算）
     */
    void recordUsage(Long userId, Long activityId, Long rechargeRecordId, Integer usageType,
                    Integer usageCount, Long relatedId, String description, String appid);

    /**
     * 查询用户使用统计
     */
    Map<String, Object> getUserUsageStats(Long userId, Long activityId);

    /**
     * 查询用户已使用次数
     */
    Integer getUsedCountByUserAndActivity(Long userId, Long activityId);

    /**
     * 查询活动的有效已使用次数（只统计来自有效充值记录的使用次数）
     */
    Integer getValidUsedCountByActivity(Long activityId);
}
