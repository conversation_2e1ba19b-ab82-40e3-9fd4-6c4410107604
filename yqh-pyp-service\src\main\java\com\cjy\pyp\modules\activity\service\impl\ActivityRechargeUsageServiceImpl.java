package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.dao.ActivityRechargeUsageDao;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 充值使用记录服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@Service("activityRechargeUsageService")
public class ActivityRechargeUsageServiceImpl extends ServiceImpl<ActivityRechargeUsageDao, ActivityRechargeUsageEntity>
        implements ActivityRechargeUsageService {

    @Autowired
    private SalesmanCommissionIntegrationService salesmanCommissionIntegrationService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<ActivityRechargeUsageEntity> wrapper = new QueryWrapper<>();

        // 按用户ID过滤
        // String userId = (String) params.get("userId");
        // if (userId != null && !userId.trim().isEmpty()) {
        //     wrapper.eq("user_id", userId);
        // }

        // 按活动ID过滤
        Object activityId = params.get("activityId");
        if (activityId != null ) {
            wrapper.eq("activity_id", activityId);
        }

        // 按使用类型过滤
        String usageType = (String) params.get("usageType");
        if (usageType != null && !usageType.trim().isEmpty()) {
            wrapper.eq("usage_type", usageType);
        }

        // 按时间排序
        wrapper.orderByDesc("id");

        IPage<ActivityRechargeUsageEntity> page = this.page(
                new Query<ActivityRechargeUsageEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }

    @Override
    public void recordUsage(Long userId, Long activityId, Long rechargeRecordId, Integer usageType,
                           Integer usageCount, Long relatedId, String description) {
        ActivityRechargeUsageEntity usage = new ActivityRechargeUsageEntity();
        usage.setUserId(userId);
        usage.setActivityId(activityId);
        usage.setRechargeRecordId(rechargeRecordId);
        usage.setUsageType(usageType);
        usage.setUsageCount(usageCount);
        usage.setRelatedId(relatedId);
        usage.setDescription(description);

        this.save(usage);

        // 如果是转发操作（usageType = 3），触发佣金计算
        if (usageType != null && usageType == 3) {
            try {
                // 需要获取appid，这里假设从ActivityEntity中获取
                // 实际实现时可能需要调整获取appid的方式
                salesmanCommissionIntegrationService.handleUserForward(usage.getId(), "default");
            } catch (Exception e) {
                // 佣金计算失败不影响主流程，只记录日志
                // logger.error("转发佣金计算失败: usageId={}, userId={}", usage.getId(), userId, e);
            }
        }
    }

    @Override
    public void recordUsage(Long userId, Long activityId, Long rechargeRecordId, Integer usageType,
                           Integer usageCount, Long relatedId, String description, String appid) {
        ActivityRechargeUsageEntity usage = new ActivityRechargeUsageEntity();
        usage.setUserId(userId);
        usage.setActivityId(activityId);
        usage.setRechargeRecordId(rechargeRecordId);
        usage.setUsageType(usageType);
        usage.setUsageCount(usageCount);
        usage.setRelatedId(relatedId);
        usage.setDescription(description);

        this.save(usage);

        // 如果是转发操作（usageType = 3），触发佣金计算
        if (usageType != null && usageType == 3) {
            try {
                salesmanCommissionIntegrationService.handleUserForward(usage.getId(), appid);
            } catch (Exception e) {
                // 佣金计算失败不影响主流程，只记录日志
                // logger.error("转发佣金计算失败: usageId={}, userId={}, appid={}", usage.getId(), userId, appid, e);
            }
        }
    }

    @Override
    public Map<String, Object> getUserUsageStats(Long userId, Long activityId) {
        return baseMapper.getUserUsageStats(userId, activityId);
    }

    @Override
    public Integer getUsedCountByUserAndActivity(Long userId, Long activityId) {
        return baseMapper.getUsedCountByUserAndActivity(userId, activityId);
    }

    @Override
    public Integer getValidUsedCountByActivity(Long activityId) {
        return baseMapper.getValidUsedCountByActivity(activityId);
    }
}
