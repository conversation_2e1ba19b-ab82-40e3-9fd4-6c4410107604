package com.cjy.pyp.modules.activity.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.constant.RedisScriptConstant;
import com.cjy.pyp.common.constant.TokenConstant;
import com.cjy.pyp.common.exception.RRException;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.vo.RechargeOrderVo;
import com.cjy.pyp.modules.activity.vo.CreateActivityPackageOrderVo;
import com.cjy.pyp.modules.activity.vo.AdminRechargeVo;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionIntegrationService;
import com.cjy.pyp.modules.channel.utils.ChannelDataUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 充值记录管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("activity/rechargerecord")
public class ActivityRechargeRecordController extends AbstractController {

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private SalesmanService salesmanService;

    @Autowired
    private SalesmanCommissionRecordService salesmanCommissionRecordService;

    @Autowired
    private SalesmanCommissionIntegrationService salesmanCommissionIntegrationService;

    @Autowired
    private ChannelDataUtils channelDataUtils;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 查询用户使用记录
     */
    @RequestMapping("/usageRecords")
    public R getUserUsageRecords(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", getUserId());
        params.put("activityId", activityId);
        params.put("page", String.valueOf(page));
        params.put("limit", String.valueOf(limit));
        List<ActivityRechargeRecordEntity> list = activityRechargeRecordService.queryPage(params);
        return R.okList(list);
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:rechargerecord:list")
    public R list(@RequestParam Map<String, Object> params) {
        List<ActivityRechargeRecordEntity> list = activityRechargeRecordService.queryPage(params);
        return R.okList(list);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:rechargerecord:info")
    public R info(@PathVariable("id") Long id) {
        ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(id);
        return R.ok().put("rechargeRecord", rechargeRecord);
    }

    /**
     * 创建充值订单
     */
    @SysLog("创建充值订单")
    @RequestMapping("/createOrder")
    @Transactional(rollbackFor = Exception.class)
    public R createOrder(@Valid @RequestBody RechargeOrderVo rechargeOrderVo) {
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()),
                rechargeOrderVo.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }

        return activityRechargeRecordService.createRechargeOrder(rechargeOrderVo, getUserId());
    }

    /**
     * 取消充值订单
     */
    @SysLog("取消充值订单")
    @RequestMapping("/cancel/{id}")
    @RequiresPermissions("activity:rechargerecord:update")
    public R cancel(@PathVariable("id") Long id) {
        return activityRechargeRecordService.cancelRechargeOrder(id);
    }

    /**
     * 删除
     */
    @SysLog("删除充值记录")
    @RequestMapping("/delete")
    @RequiresPermissions("activity:rechargerecord:delete")
    public R delete(@RequestBody Long[] ids) {
        activityRechargeRecordService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 管理端充值接口（支持套餐充值和系统赠送）
     */
    @SysLog("管理端充值")
    @RequestMapping("/recharge")
    // @RequiresPermissions("activity:rechargerecord:recharge")
    @Transactional(rollbackFor = Exception.class)
    public R recharge(@Valid @RequestBody AdminRechargeVo rechargeVo) {
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()),
                rechargeVo.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }

        return activityRechargeRecordService.adminRecharge(rechargeVo, getUserId());
    }

    /**
     * 审核通过退款
     */
    @SysLog("审核通过退款")
    @RequestMapping("/approveRefund")
    @RequiresPermissions("activity:rechargerecord:refund")
    @Transactional(rollbackFor = Exception.class)
    public R approveRefund(@RequestBody Map<String, Object> params) {
        Long id = Long.valueOf(params.get("id").toString());
        String orderSn = (String) params.get("orderSn");

        return activityRechargeRecordService.approveRefund(id, orderSn, getUserId());
    }

    /**
     * 拒绝退款
     */
    @SysLog("拒绝退款")
    @RequestMapping("/rejectRefund")
    @RequiresPermissions("activity:rechargerecord:refund")
    @Transactional(rollbackFor = Exception.class)
    public R rejectRefund(@RequestBody Map<String, Object> params) {
        Long id = Long.valueOf(params.get("id").toString());
        String orderSn = (String) params.get("orderSn");
        String rejectReason = (String) params.get("rejectReason");

        return activityRechargeRecordService.rejectRefund(id, orderSn, rejectReason, getUserId());
    }

    /**
     * 系统赠送次数（支持GET参数方式调用）
     */
    @SysLog("系统赠送次数")
    @RequestMapping("/gift")
    // @RequiresPermissions("activity:rechargerecord:gift")
    @Transactional(rollbackFor = Exception.class)
    public R gift(@RequestParam("activityId") Long activityId,
            @RequestParam("count") Integer count,
            @RequestParam(value = "validDays", required = false) Integer validDays,
            @RequestParam(value = "remarks", required = false) String remarks,
            @RequestParam(value = "repeatToken", required = false) String repeatToken,
            @RequestParam(value = "appid", required = false) String appid) {

        // 如果有防重令牌，进行验证
        if (repeatToken != null && !repeatToken.isEmpty()) {
            Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                    Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), repeatToken);
            if (result == 0L) {
                throw new RRException("不能重复提交");
            }
        }

        return activityRechargeRecordService.giftCountWithValidDays(activityId, count, validDays, remarks, appid,
                getUserId());
    }

    /**
     * 查询用户充值统计
     */
    @RequestMapping("/userStats")
    // @RequiresPermissions("activity:rechargerecord:list")
    public R getUserStats(@RequestParam("userId") Long userId, @RequestParam("activityId") Long activityId) {
        Map<String, Object> stats = activityRechargeRecordService.getUserRechargeStats(userId, activityId);
        return R.ok().put("stats", stats);
    }

    /**
     * 查询用户可用次数
     */
    @RequestMapping("/availableCount")
    public R getAvailableCount(@RequestParam("userId") Long userId, @RequestParam("activityId") Long activityId) {
        Integer count = activityRechargeRecordService.getAvailableCountByUserAndActivity(userId, activityId);
        return R.ok().put("availableCount", count);
    }

    /**
     * 创建活动套餐订单
     */
    @SysLog("创建活动套餐订单")
    @RequestMapping("/createActivityPackageOrder")
    @Transactional(rollbackFor = Exception.class)
    public R createActivityPackageOrder(@Valid @RequestBody CreateActivityPackageOrderVo orderVo) {
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), orderVo.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }

        return activityRechargeRecordService.createActivityPackageOrder(orderVo, getUserId());
    }

    // ==================== 业务员订单管理接口 ====================

    /**
     * 业务员订单列表
     */
    @RequestMapping("/salesmanOrderList")
    @RequiresPermissions("salesman:order:list")
    public R salesmanOrderList(@RequestParam Map<String, Object> params) {
        // 应用渠道权限过滤
        Long userId = getUser().getUserId();
        java.util.List<Long> accessibleChannelIds = channelDataUtils.getAccessibleChannelIds(userId);
        if (accessibleChannelIds != null && !accessibleChannelIds.isEmpty()) {
            params.put("channelIds", accessibleChannelIds);
        }

        // 直接调用 DAO 的 SQL 查询
        List<ActivityRechargeRecordEntity> page = activityRechargeRecordService.querySalesmanOrderPage(params);
        return R.okList(page);
    }

    /**
     * 业务员订单统计
     */
    @RequestMapping("/salesmanOrderStats")
    // @RequiresPermissions("salesman:order:stats")
    public R salesmanOrderStats(@RequestParam Map<String, Object> params) {
        // 应用渠道权限过滤
        Long userId = getUser().getUserId();
        java.util.List<Long> accessibleChannelIds = channelDataUtils.getAccessibleChannelIds(userId);
        if (accessibleChannelIds != null && !accessibleChannelIds.isEmpty()) {
            params.put("channelIds", accessibleChannelIds);
        }

        // 直接调用 DAO 的 SQL 查询统计
        Map<String, Object> stats = activityRechargeRecordService.getSalesmanOrderStats(params);
        return R.ok().put("stats", stats);
    }

    /**
     * 更新业务员订单佣金
     */
    @RequestMapping("/updateSalesmanOrderCommission")
    @RequiresPermissions("salesman:order:update")
    public R updateSalesmanOrderCommission(@RequestBody Map<String, Object> params) {
        Long orderId = Long.parseLong(params.get("id").toString());
        java.math.BigDecimal commissionRate = new java.math.BigDecimal(params.get("commissionRate").toString());
        java.math.BigDecimal commissionAmount = new java.math.BigDecimal(params.get("commissionAmount").toString());

        // 获取订单信息
        ActivityRechargeRecordEntity order = activityRechargeRecordService.getById(orderId);
        if (order == null) {
            return R.error("订单不存在");
        }

        // 查找现有的佣金记录
        QueryWrapper<SalesmanCommissionRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("business_id", orderId)
                .eq("business_type", "1") // 充值订单
                .ne("settlement_status", 2); // 排除已取消的

        java.util.List<SalesmanCommissionRecordEntity> existingRecords = salesmanCommissionRecordService.list(wrapper);

        // 删除现有的佣金记录
        if (!existingRecords.isEmpty()) {
            java.util.List<Long> recordIds = existingRecords.stream()
                    .map(SalesmanCommissionRecordEntity::getId)
                    .collect(java.util.stream.Collectors.toList());
            salesmanCommissionRecordService.removeByIds(recordIds);
        }

        // 创建新的佣金记录
        SalesmanCommissionRecordEntity commissionRecord = new SalesmanCommissionRecordEntity();
        commissionRecord.setSalesmanId(order.getSalesmanId());
        commissionRecord.setBusinessId(orderId);
        commissionRecord.setBusinessType("1"); // 充值订单
        commissionRecord.setCommissionRate(commissionRate);
        commissionRecord.setCommissionAmount(commissionAmount);
        commissionRecord.setOrderAmount(order.getPayAmount());
        commissionRecord.setSettlementStatus(0); // 未结算
        commissionRecord.setCreateOn(new java.util.Date());
        commissionRecord.setAppid(order.getAppid());

        salesmanCommissionRecordService.save(commissionRecord);

        return R.ok();
    }

    /**
     * 删除业务员订单关联
     */
    @RequestMapping("/deleteSalesmanOrder")
    @RequiresPermissions("salesman:order:delete")
    public R deleteSalesmanOrder(@RequestBody Long[] ids) {
        // 将订单的业务员ID设置为null，而不是删除订单本身
        for (Long id : ids) {
            ActivityRechargeRecordEntity order = activityRechargeRecordService.getById(id);
            if (order != null) {
                order.setSalesmanId(null);
                activityRechargeRecordService.updateById(order);

                // 同时删除相关的佣金记录
                QueryWrapper<SalesmanCommissionRecordEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("business_id", id);
                salesmanCommissionRecordService.remove(wrapper);
            }
        }

        return R.ok();
    }

    /**
     * 直接修改订单状态
     */
    @SysLog("修改充值订单状态")
    @RequestMapping("/updateStatus")
    @RequiresPermissions("activity:rechargerecord:update")
    @Transactional(rollbackFor = Exception.class)
    public R updateStatus(@RequestBody Map<String, Object> params) {
        Long id = Long.parseLong(params.get("id").toString());
        Integer status = Integer.parseInt(params.get("status").toString());
        String remarks = params.get("remarks") != null ? params.get("remarks").toString() : null;

        ActivityRechargeRecordEntity record = activityRechargeRecordService.getById(id);
        if (record == null) {
            return R.error("充值订单不存在");
        }

        Integer oldStatus = record.getStatus();

        // 状态变更逻辑验证
        if (oldStatus.equals(status)) {
            return R.error("订单状态未发生变化");
        }

        // 如果从待支付改为已支付，需要处理支付逻辑
        if (oldStatus == 0 && status == 1) {
            record.setStatus(1);
            record.setPayTime(new Date());
            record.setPayType(3); // 手动标记为已支付
            record.setPayTransaction("MANUAL_" + System.currentTimeMillis());

            if (remarks != null && !remarks.trim().isEmpty()) {
                record.setRemarks(record.getRemarks() != null ? record.getRemarks() + "; " + remarks : remarks);
            }

            activityRechargeRecordService.updateById(record);

            // 触发佣金计算
            try {
                salesmanCommissionIntegrationService.handleRechargeCompleted(record.getId(), record.getAppid());
            } catch (Exception e) {
                // 佣金计算失败不影响主流程，只记录日志
            }

            return R.ok("订单状态已更新为已支付");
        }

        // 如果改为已取消状态
        if (status == 2) {
            // if (oldStatus == 1) {
            //     return R.error("已支付的订单不能直接取消，请使用退款功能");
            // }
            record.setStatus(2);
            if (remarks != null && !remarks.trim().isEmpty()) {
                record.setRemarks(record.getRemarks() != null ? record.getRemarks() + "; " + remarks : remarks);
            }
            activityRechargeRecordService.updateById(record);
            return R.ok("订单状态已更新为已取消");
        }

        // 如果改为已退款状态
        if (status == 3) {
            if (oldStatus != 1) {
                return R.error("只有已支付的订单才能设置为已退款");
            }
            record.setStatus(3);
            record.setRefundTime(new Date());
            if (remarks != null && !remarks.trim().isEmpty()) {
                record.setRemarks(record.getRemarks() != null ? record.getRemarks() + "; " + remarks : remarks);
            }
            activityRechargeRecordService.updateById(record);
            return R.ok("订单状态已更新为已退款");
        }

        // 其他状态变更
        record.setStatus(status);
        if (remarks != null && !remarks.trim().isEmpty()) {
            record.setRemarks(record.getRemarks() != null ? record.getRemarks() + "; " + remarks : remarks);
        }
        activityRechargeRecordService.updateById(record);

        return R.ok("订单状态已更新");
    }

    // ==================== 辅助方法 ====================

    /**
     * 应用业务员订单筛选条件
     */
    private void applySalesmanOrderFilters(QueryWrapper<ActivityRechargeRecordEntity> wrapper,
            Map<String, Object> params) {
        String salesmanId = (String) params.get("salesmanId");
        if (StringUtils.isNotBlank(salesmanId)) {
            wrapper.eq("salesman_id", salesmanId);
        }

        String orderType = (String) params.get("orderType");
        if (StringUtils.isNotBlank(orderType)) {
            if ("1".equals(orderType)) {
                wrapper.eq("recharge_type", 4);
            } else if ("2".equals(orderType)) {
                wrapper.in("recharge_type", Arrays.asList(1, 2));
            }
        }

        String orderStatus = (String) params.get("orderStatus");
        if (StringUtils.isNotBlank(orderStatus)) {
            wrapper.eq("status", orderStatus);
        }

        String userName = (String) params.get("userName");
        if (StringUtils.isNotBlank(userName)) {
            wrapper.like("user_name", userName);
        }

        String userMobile = (String) params.get("userMobile");
        if (StringUtils.isNotBlank(userMobile)) {
            wrapper.like("user_mobile", userMobile);
        }

        String salesmanName = (String) params.get("salesmanName");
        if (StringUtils.isNotBlank(salesmanName)) {
            QueryWrapper<SalesmanEntity> salesmanWrapper = new QueryWrapper<>();
            salesmanWrapper.like("name", salesmanName);
            java.util.List<SalesmanEntity> salesmen = salesmanService.list(salesmanWrapper);
            if (!salesmen.isEmpty()) {
                java.util.List<Long> salesmanIds = salesmen.stream()
                        .map(SalesmanEntity::getId)
                        .collect(java.util.stream.Collectors.toList());
                wrapper.in("salesman_id", salesmanIds);
            } else {
                wrapper.eq("salesman_id", -1); // 不存在的ID，返回空结果
            }
        }
    }

    /**
     * 获取订单状态描述
     */
    private String getOrderStatusDesc(Integer status) {
        if (status == null)
            return "未知";
        switch (status) {
            case 0:
                return "待支付";
            case 1:
                return "已支付";
            case 2:
                return "已取消";
            case 3:
                return "已退款";
            default:
                return "未知";
        }
    }

    /**
     * 获取佣金信息
     */
    private Map<String, Object> getCommissionInfo(Long orderId) {
        Map<String, Object> result = new HashMap<>();

        // 查询佣金记录
        QueryWrapper<SalesmanCommissionRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("business_id", orderId)
                .ne("settlement_status", 2); // 排除已取消的

        java.util.List<SalesmanCommissionRecordEntity> records = salesmanCommissionRecordService.list(wrapper);

        if (!records.isEmpty()) {
            // 取最新的佣金记录
            SalesmanCommissionRecordEntity record = records.get(0);
            result.put("commissionRate", record.getCommissionRate());
            result.put("commissionAmount", record.getCommissionAmount());
        } else {
            result.put("commissionRate", null);
            result.put("commissionAmount", null);
        }

        return result;
    }
}
