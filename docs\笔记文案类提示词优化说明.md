# 笔记文案类提示词优化说明

## 优化背景

原有的笔记文案类提示词生成的内容比较模板化，缺乏灵魂和个性化，用户反馈生成的内容"很没有灵魂"。为了提升生成内容的质量和可用性，我们对笔记类型的提示词进行了全面优化。

## 优化范围

本次优化专注于**笔记文案类**内容，包括以下类型：
- ✅ 小红书 (xiaohongshu) - 图文笔记
- ✅ 大众点评 (dianping) - 商户评价笔记
- ✅ 美团点评 (meituan) - 商户评价笔记
- ✅ 微信朋友圈 (weixin) - 社交分享笔记
- ✅ 携程点评 (ctrip_review) - 旅游点评笔记
- ✅ 携程笔记 (ctrip_notes) - 旅游笔记
- ✅ 抖音点评 (douyin_review) - 商户点评笔记
- ✅ 通用文案 (general) - 通用笔记

**排除的类型**（视频类，不需要优化）：
- ❌ 抖音 (douyin) - 短视频类
- ❌ 快手 (kuaishou) - 短视频类

## 核心改进

1. **增强真实感** - 要求有真实的个人体验和情感表达
2. **提升实用性** - 提供具体可操作的建议和实用信息
3. **优化语言风格** - 更自然流畅，贴近用户习惯
4. **增加个性化** - 避免千篇一律，要有独特见解
5. **强化价值导向** - 让内容对读者有实际帮助
6. **放宽字数限制** - 让点评类文案有更多空间展现详细体验 ⭐ **新增**

### 字数限制优化对比

| 平台类型 | 原标题限制 | 新标题限制 | 原内容限制 | 新内容限制 | 提升幅度 |
|---------|-----------|-----------|-----------|-----------|---------|
| 大众点评 | 25字以内 | 30字以内 | 100-200字 | 150-300字 | +50% |
| 美团点评 | 25字以内 | 30字以内 | 100-200字 | 150-300字 | +50% |
| 携程点评 | - | 35字以内 | - | 200-400字 | 大幅提升 |
| 抖音点评 | 18字以内 | 25字以内 | 90字以内 | 120-250字 | +180% |

**字数优化的好处：**
- 📝 **更详细的体验描述** - 有足够空间描述具体的消费过程和感受
- 💡 **更实用的建议** - 可以提供更多有价值的消费提醒和技巧
- 🎯 **更好的参考价值** - 其他用户能获得更全面的决策信息
- ✨ **更有说服力** - 详细的描述增加内容的可信度和影响力

## 优化内容

### 1. 小红书笔记优化

**优化前问题：**
- 内容空洞，缺乏真实感
- 语言官方化，缺乏个人特色
- 缺乏实用价值和操作性

**优化后特点：**
- 强调真实的个人体验和情感表达
- 要求具体的场景细节和画面感
- 提供实用价值和可操作建议
- 自然流畅的朋友式分享语言
- 适度的情感起伏和真实感受
- 避免营销化语言，保持种草自然性
- 独特的个人视角和见解

**新的创作指导：**
1. 开头要有吸引力：用有趣场景、意外发现或强烈感受开始
2. 中间要有干货：提供具体信息、技巧或攻略
3. 结尾要有共鸣：用疑问句、感叹句或互动语言引发参与

### 2. 携程笔记优化

**优化前问题：**
- 旅行描述空泛，缺乏实用信息
- 缺乏个人故事和独特发现
- 语言平淡，无法激发旅行欲望

**优化后特点：**
- 真实的旅行体验和感受
- 实用的旅行信息：交通、住宿、美食、景点攻略
- 个人旅行故事和独特发现
- 具体的时间、地点、价格等实用信息
- 美好的画面描述，让读者产生向往
- 贴心的旅行建议和注意事项
- 轻松愉快的语言，传递旅行美好

**新的创作框架：**
1. 开场吸引：美好旅行瞬间或意外发现
2. 体验分享：详细描述真实旅行体验
3. 实用攻略：提供具体旅行信息和建议
4. 情感升华：表达旅行带来的美好感受

### 3. 大众点评优化

**优化前问题：**
- 评价内容空洞，缺乏具体体验
- 缺乏实用的消费信息
- 语言过于官方化，缺乏真实感

**优化后特点：**
- 真实的消费体验和具体感受
- 详细的服务过程和产品体验描述
- 具体的价格、环境、服务等实用信息
- 个人的真实评价和建议
- 客观但有温度的语言，像朋友推荐
- 对其他消费者有实际参考价值
- 避免过度夸赞或贬低，保持真实性

### 4. 美团点评优化

**优化前问题：**
- 缺乏性价比分析
- 优惠信息不突出
- 消费建议不实用

**优化后特点：**
- 真实的消费体验和性价比分析
- 突出优惠信息和实际花费情况
- 具体的消费建议和省钱技巧
- 详细的产品和服务描述
- 实用导向的语言，帮助决策
- 明确的推荐理由和注意事项
- 注重实际体验，避免夸大宣传

### 5. 微信朋友圈优化

**优化前问题：**
- 内容过于商业化
- 缺乏分享价值
- 语言不够自然亲切

**优化后特点：**
- 有分享价值，能引起朋友关注和互动
- 自然亲切的语言，像朋友间真诚分享
- 个人的真实体验和感受
- 准确可信的信息，避免夸大宣传
- 适合社交场景，不过于商业化
- 有一定的话题性和讨论价值
- 简洁明了，易于阅读和理解

### 6. 携程点评优化

**优化前问题：**
- 旅游信息不够专业
- 缺乏实用的预订建议
- 参考价值不高

**优化后特点：**
- 详细的旅游体验和真实感受
- 实用的旅游信息和专业建议
- 具体的服务质量和设施描述
- 突出性价比和预订建议
- 对其他旅客有实际参考价值
- 包含交通、住宿、景点等全面信息
- 专业但易懂的语言，有说服力

### 7. 抖音点评优化

**优化前问题：**
- 语言过于正式，不符合抖音用户习惯
- 缺乏年轻化表达和网感
- 内容缺乏话题性和传播性

**优化后特点：**
- 真实的消费体验和年轻化表达
- 活泼有趣的语言，符合抖音用户习惯
- 具体的产品和服务体验描述
- 突出视觉效果和氛围感受
- 有网感和流行元素，容易传播
- 实用的消费建议和避雷指南
- 有话题性，能引起互动

### 8. 新增知乎笔记类型

**特点：**
- 深度思考和独特见解
- 有价值的知识和经验分享
- 逻辑清晰，论证有力
- 个人实践经验和案例
- 专业但不失亲和力
- 能解决读者实际问题
- 避免空洞理论，注重实操性

### 4. 通用文案优化

**优化重点：**
- 强调实用价值，能解决实际问题
- 通俗易懂，适合大众阅读
- 清晰的逻辑结构和条理
- 个人经验和见解分享
- 避免空洞理论，要有具体例子
- 有一定深度，不过于浅显
- 适合多平台传播分享

## 技术实现

### 1. 数据库迁移文件
- 文件：`V1.0.11__optimize_notes_prompt_templates.sql`
- 更新现有笔记类配置的requirements、style、prompt_template字段
- 优化小红书、大众点评、美团点评、微信朋友圈、携程点评、携程笔记、通用文案等配置
- 新增知乎笔记等新的笔记类型配置

### 2. 管理界面优化
- 在广告类型配置管理页面添加"优化笔记类提示词"按钮
- 一键应用所有优化配置
- 提供友好的操作反馈

### 3. 后端API接口
- 新增`/activity/adtypeconfig/optimizeNotesPrompts`接口
- 在AdTypeConfigService中添加optimizeNotesPrompts方法
- 分别优化不同类型的提示词配置

## 使用方法

### 管理员操作
1. 登录后台管理系统
2. 进入"活动管理" -> "广告类型配置"
3. 点击"优化笔记类提示词"按钮
4. 确认操作后等待优化完成

### 效果验证
1. 在文案生成页面选择小红书、携程笔记等类型
2. 输入关键词生成文案
3. 对比优化前后的内容质量和个性化程度

## 预期效果

### 内容质量提升
- 生成的内容更有真实感和个人特色
- 提供更多实用价值和可操作建议
- 语言更自然流畅，贴近用户习惯

### 用户体验改善
- 减少二次编辑的需求
- 提高内容的直接可用性
- 增强内容的传播价值

### 平台适配性
- 更好地适应各平台的内容特点
- 符合平台用户的阅读习惯
- 提高内容在平台上的表现效果

## 注意事项

1. **备份重要性**：优化前建议备份原有配置
2. **测试验证**：优化后需要充分测试各类型文案生成效果
3. **用户反馈**：持续收集用户反馈，进一步优化提示词
4. **版本控制**：通过数据库迁移文件管理配置变更

## 后续优化方向

1. **个性化定制**：根据不同行业和用户需求定制提示词
2. **智能学习**：基于用户反馈和使用数据优化提示词
3. **多样化风格**：为同一平台提供多种风格选择
4. **实时调优**：支持在线调整和测试提示词效果

---

*本优化方案旨在解决笔记文案类内容"没有灵魂"的问题，通过更精细化的提示词设计，让AI生成的内容更有温度、更有价值、更具个性化特色。*
